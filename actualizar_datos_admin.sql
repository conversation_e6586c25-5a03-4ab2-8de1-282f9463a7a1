-- Script para actualizar los datos del administrador con información más completa
-- Ejecutar después de tener la base de datos creada

USE escuela_nv;

-- Actualizar información personal del administrador
UPDATE personas 
SET 
    nombres = '<PERSON>',
    apellido_paterno = 'Molina',
    apellido_materno = 'Aguirre',
    dni = '12345678',
    fecha_nacimiento = '1980-05-15',
    edad = 44,
    sexo = 'femenino',
    direccion = 'Av. Educación 123, San Isidro, Lima',
    telefono = '991663041'
WHERE usuario_id = 1;

-- Actualizar información del administrador
UPDATE administradores 
SET 
    cargo = 'Directora General',
    departamento = 'Dirección Académica',
    fecha_contratacion = '2020-01-15'
WHERE persona_id = 1;

-- Insertar contacto de emergencia para el administrador
INSERT INTO contactos_emergencia (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id)
VALUES (1, '<PERSON> (<PERSON><PERSON>oso)', '987654321', '991234567', '<EMAIL>', 5)
ON DUPLICATE KEY UPDATE
    nombre_contacto = 'Carlos Molina (Esposo)',
    telefono_principal = '987654321',
    telefono_alternativo = '991234567',
    email = '<EMAIL>',
    tipo_contacto_id = 5;

-- Verificar que los datos se actualizaron correctamente
SELECT 
    u.nombre_usuario,
    u.email,
    u.rol,
    p.nombres,
    p.apellido_paterno,
    p.apellido_materno,
    p.dni,
    p.fecha_nacimiento,
    p.sexo,
    p.direccion,
    p.telefono,
    a.cargo,
    a.departamento,
    a.fecha_contratacion
FROM usuarios u
INNER JOIN personas p ON u.id = p.usuario_id
INNER JOIN administradores a ON p.id = a.persona_id
WHERE u.nombre_usuario = 'admin';

-- Verificar contacto de emergencia
SELECT 
    ce.nombre_contacto,
    ce.telefono_principal,
    ce.telefono_alternativo,
    ce.email,
    tce.nombre as tipo_contacto
FROM contactos_emergencia ce
LEFT JOIN tipos_contacto_emergencia tce ON ce.tipo_contacto_id = tce.id
WHERE ce.persona_id = 1;
