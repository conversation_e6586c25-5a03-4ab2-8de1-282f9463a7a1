-- Script para insertar SOLO datos de prueba adicionales
-- Ejecutar después de crear la base de datos con base_datos_escuela.sql
-- IMPORTANTE: La base de datos YA incluye un administrador, NO lo duplicamos

USE escuela_nv;

-- =============================================
-- LIMPIAR DATOS DE PRUEBA ANTERIORES (OPCIONAL)
-- =============================================
-- Descomenta estas líneas si quieres empezar limpio:
-- DELETE FROM padre_estudiante WHERE id > 0;
-- DELETE FROM estudiantes WHERE id > 0;
-- DELETE FROM maestros WHERE id > 0;
-- DELETE FROM padres WHERE id > 0;
-- DELETE FROM personas WHERE id > 1; -- Mantener el administrador (ID=1)
-- DELETE FROM usuarios WHERE id > 1; -- Mantener el administrador (ID=1)

-- =============================================
-- VERIFICAR QUE EL ADMINISTRADOR EXISTE
-- =============================================
-- El administrador ya existe con:
-- Usuario: admin, Contraseña: password, Email: <EMAIL>

-- =============================================
-- INSERTAR MAESTROS DE PRUEBA
-- =============================================

-- MAESTRO 1: Juan Carlos Pérez (Matemáticas - Primaria)
-- Usuario: jperez, Contraseña: maestro123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('jperez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'maestro');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Juan Carlos', 'Pérez', 'Rodríguez', '23456789', '1985-03-20', 'masculino', 'Jr. Los Maestros 456, Lima', '987654322');

INSERT INTO maestros (persona_id, especialidad, nivel_educativo, grado_tutor, fecha_contratacion) VALUES 
(LAST_INSERT_ID(), 'Matemáticas', 'primaria', 'primaria-3', '2020-03-01');

-- MAESTRO 2: Ana María Flores (Comunicación - Inicial)
-- Usuario: aflores, Contraseña: maestro123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('aflores', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'maestro');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Ana María', 'Flores', 'Vásquez', '34567890', '1988-07-12', 'femenino', 'Av. Educación 789, Lima', '987654323');

INSERT INTO maestros (persona_id, especialidad, nivel_educativo, grado_tutor, fecha_contratacion) VALUES 
(LAST_INSERT_ID(), 'Comunicación', 'inicial', 'inicial-5', '2019-02-15');

-- MAESTRO 3: Carlos Eduardo Mendoza (Ciencia y Tecnología - Primaria)
-- Usuario: cmendoza, Contraseña: maestro123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('cmendoza', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'maestro');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Carlos Eduardo', 'Mendoza', 'Silva', '45678901', '1982-11-08', 'masculino', 'Calle Ciencia 321, Lima', '987654324');

INSERT INTO maestros (persona_id, especialidad, nivel_educativo, grado_tutor, fecha_contratacion) VALUES 
(LAST_INSERT_ID(), 'Ciencia y Tecnología', 'primaria', 'primaria-5', '2018-01-10');

-- =============================================
-- INSERTAR PADRES DE PRUEBA
-- =============================================

-- PADRE 1: Roberto Sánchez (Padre)
-- Usuario: rsanchez, Contraseña: padre123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('rsanchez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'padre');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Roberto', 'Sánchez', 'Torres', '56789012', '1978-09-25', 'masculino', 'Av. Familia 654, Lima', '987654325');

INSERT INTO padres (persona_id, tipo_apoderado) VALUES 
(LAST_INSERT_ID(), 'padre');

-- MADRE 1: Carmen Rosa Díaz (Madre)
-- Usuario: cdiaz, Contraseña: madre123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('cdiaz', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'padre');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Carmen Rosa', 'Díaz', 'Morales', '67890123', '1980-12-03', 'femenino', 'Av. Familia 654, Lima', '987654326');

INSERT INTO padres (persona_id, tipo_apoderado) VALUES 
(LAST_INSERT_ID(), 'madre');

-- TUTOR LEGAL: Luis Alberto Vargas
-- Usuario: lvargas, Contraseña: tutor123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES 
('lvargas', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'padre');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES 
(LAST_INSERT_ID(), 'Luis Alberto', 'Vargas', 'Herrera', '78901234', '1975-04-18', 'masculino', 'Jr. Tutores 987, Lima', '987654327');

INSERT INTO padres (persona_id, tipo_apoderado) VALUES 
(LAST_INSERT_ID(), 'tutor legal');

-- =============================================
-- INSERTAR ESTUDIANTES DE PRUEBA
-- =============================================

-- ESTUDIANTE 1: Sofía Sánchez Díaz (Hija de Roberto y Carmen)
-- Usuario: ssanchez, Contraseña: estudiante123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES
('ssanchez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'estudiante');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES
(LAST_INSERT_ID(), 'Sofía', 'Sánchez', 'Díaz', '89012345', '2015-06-10', 'femenino', 'Av. Familia 654, Lima', '987654325');

-- Obtener el ID de la persona recién insertada para el estudiante
SET @sofia_persona_id = LAST_INSERT_ID();

-- Obtener el ID del maestro tutor (Juan Carlos Pérez)
SET @tutor_id = (SELECT m.id FROM maestros m
                 INNER JOIN personas p ON m.persona_id = p.id
                 INNER JOIN usuarios u ON p.usuario_id = u.id
                 WHERE u.nombre_usuario = 'jperez');

INSERT INTO estudiantes (persona_id, grado_actual, anio_escolar, maestro_tutor_id) VALUES
(@sofia_persona_id, 'primaria-3', 2025, @tutor_id);

-- ESTUDIANTE 2: Diego Martínez López
-- Usuario: dmartinez, Contraseña: estudiante123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES
('dmartinez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'estudiante');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES
(LAST_INSERT_ID(), 'Diego', 'Martínez', 'López', '90123456', '2016-08-22', 'masculino', 'Calle Estudiantes 159, Lima', '987654328');

SET @diego_persona_id = LAST_INSERT_ID();

INSERT INTO estudiantes (persona_id, grado_actual, anio_escolar, maestro_tutor_id) VALUES
(@diego_persona_id, 'primaria-2', 2025, @tutor_id);

-- ESTUDIANTE 3: Valentina Vargas (Bajo tutela de Luis Alberto)
-- Usuario: vvargas, Contraseña: estudiante123
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES
('vvargas', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'estudiante');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES
(LAST_INSERT_ID(), 'Valentina', 'Vargas', 'Herrera', '01234567', '2018-02-14', 'femenino', 'Jr. Tutores 987, Lima', '987654327');

SET @valentina_persona_id = LAST_INSERT_ID();

-- Obtener el ID del maestro tutor (Ana María Flores)
SET @tutor_ana_id = (SELECT m.id FROM maestros m
                     INNER JOIN personas p ON m.persona_id = p.id
                     INNER JOIN usuarios u ON p.usuario_id = u.id
                     WHERE u.nombre_usuario = 'aflores');

INSERT INTO estudiantes (persona_id, grado_actual, anio_escolar, maestro_tutor_id) VALUES
(@valentina_persona_id, 'inicial-5', 2025, @tutor_ana_id);

-- =============================================
-- INSERTAR RELACIONES PADRE-ESTUDIANTE
-- =============================================

-- Obtener IDs de padres y estudiantes para las relaciones
SET @roberto_padre_id = (SELECT pa.id FROM padres pa
                         INNER JOIN personas p ON pa.persona_id = p.id
                         INNER JOIN usuarios u ON p.usuario_id = u.id
                         WHERE u.nombre_usuario = 'rsanchez');

SET @carmen_padre_id = (SELECT pa.id FROM padres pa
                        INNER JOIN personas p ON pa.persona_id = p.id
                        INNER JOIN usuarios u ON p.usuario_id = u.id
                        WHERE u.nombre_usuario = 'cdiaz');

SET @luis_padre_id = (SELECT pa.id FROM padres pa
                      INNER JOIN personas p ON pa.persona_id = p.id
                      INNER JOIN usuarios u ON p.usuario_id = u.id
                      WHERE u.nombre_usuario = 'lvargas');

SET @sofia_estudiante_id = (SELECT e.id FROM estudiantes e
                           INNER JOIN personas p ON e.persona_id = p.id
                           INNER JOIN usuarios u ON p.usuario_id = u.id
                           WHERE u.nombre_usuario = 'ssanchez');

SET @valentina_estudiante_id = (SELECT e.id FROM estudiantes e
                               INNER JOIN personas p ON e.persona_id = p.id
                               INNER JOIN usuarios u ON p.usuario_id = u.id
                               WHERE u.nombre_usuario = 'vvargas');

-- Sofía es hija de Roberto (padre) y Carmen (madre)
INSERT INTO padre_estudiante (padre_id, estudiante_id) VALUES
(@roberto_padre_id, @sofia_estudiante_id), -- Roberto -> Sofía
(@carmen_padre_id, @sofia_estudiante_id);  -- Carmen -> Sofía

-- Valentina está bajo tutela de Luis Alberto
INSERT INTO padre_estudiante (padre_id, estudiante_id) VALUES
(@luis_padre_id, @valentina_estudiante_id); -- Luis Alberto -> Valentina

-- =============================================
-- INSERTAR BIMESTRES 2025 (SOLO SI NO EXISTEN)
-- =============================================
-- Verificar si ya existen bimestres para 2025, si no, insertarlos
INSERT INTO bimestres (numero, nombre, fecha_inicio, fecha_fin, anio_escolar, activo)
SELECT * FROM (
    SELECT 1 as numero, 'Primer Bimestre' as nombre, '2025-03-01' as fecha_inicio, '2025-05-15' as fecha_fin, 2025 as anio_escolar, TRUE as activo
    UNION ALL
    SELECT 2, 'Segundo Bimestre', '2025-05-16', '2025-07-31', 2025, FALSE
    UNION ALL
    SELECT 3, 'Tercer Bimestre', '2025-08-01', '2025-10-15', 2025, FALSE
    UNION ALL
    SELECT 4, 'Cuarto Bimestre', '2025-10-16', '2025-12-20', 2025, FALSE
) AS nuevos_bimestres
WHERE NOT EXISTS (
    SELECT 1 FROM bimestres WHERE anio_escolar = 2025
);

-- =============================================
-- RESUMEN DE USUARIOS CREADOS
-- =============================================
/*
ADMINISTRADOR (YA EXISTE EN LA BASE DE DATOS):
- Usuario: admin
- Contraseña: password

MAESTROS:
- Usuario: jperez (Juan Carlos Pérez - Matemáticas)
- Usuario: aflores (Ana María Flores - Comunicación)
- Usuario: cmendoza (Carlos Eduardo Mendoza - Ciencia y Tecnología)
- Contraseña para todos: maestro123

PADRES:
- Usuario: rsanchez (Roberto Sánchez - Padre)
- Usuario: cdiaz (Carmen Rosa Díaz - Madre)
- Usuario: lvargas (Luis Alberto Vargas - Tutor Legal)
- Contraseñas: padre123, madre123, tutor123 respectivamente

ESTUDIANTES:
- Usuario: ssanchez (Sofía Sánchez Díaz - Primaria 3)
- Usuario: dmartinez (Diego Martínez López - Primaria 2)
- Usuario: vvargas (Valentina Vargas - Inicial 5)
- Contraseña para todos: estudiante123

NOTA: Todas las contraseñas están hasheadas con password_hash() de PHP
El hash usado corresponde a las contraseñas mencionadas arriba.
*/
