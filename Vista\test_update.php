<?php
session_start();
require_once '../Modelo/Conexion.php';
require_once '../Controlador/AuthController.php';

// Simular login del administrador para pruebas
$_SESSION['usuario_id'] = 1;
$_SESSION['nombre_usuario'] = 'admin';
$_SESSION['email'] = '<EMAIL>';
$_SESSION['rol'] = 'administrador';
$_SESSION['nombres'] = 'Administrador';
$_SESSION['apellido_paterno'] = 'Sistema';
$_SESSION['apellido_materno'] = 'Escuela';

echo "<h2>Test de actualización directa</h2>";

try {
    $pdo = Conexion::getConexion();
    
    // Datos de prueba
    $nombres = 'Patricia Elena';
    $apellidoPaterno = 'Molina';
    $apellidoMaterno = 'Aguirre';
    $dni = '12345678';
    $fechaNacimiento = '1980-05-15';
    $sexo = 'femenino';
    $direccion = 'Av. Educación 123, San Isidro, Lima';
    $telefono = '991663041';
    $usuarioId = 1;
    
    echo "<h3>Datos que se van a actualizar:</h3>";
    echo "<ul>";
    echo "<li>Nombres: $nombres</li>";
    echo "<li>Apellido paterno: $apellidoPaterno</li>";
    echo "<li>Apellido materno: $apellidoMaterno</li>";
    echo "<li>DNI: $dni</li>";
    echo "<li>Fecha nacimiento: $fechaNacimiento</li>";
    echo "<li>Sexo: $sexo</li>";
    echo "<li>Dirección: $direccion</li>";
    echo "<li>Teléfono: $telefono</li>";
    echo "<li>Usuario ID: $usuarioId</li>";
    echo "</ul>";
    
    // Verificar que existe el registro
    $sqlCheck = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
    $stmtCheck = $pdo->prepare($sqlCheck);
    $stmtCheck->bindParam(':usuario_id', $usuarioId);
    $stmtCheck->execute();
    $persona = $stmtCheck->fetch();
    
    if (!$persona) {
        echo "<h3>❌ No existe registro en tabla personas para usuario_id = $usuarioId</h3>";
        
        // Crear el registro
        echo "<p>Creando registro en tabla personas...</p>";
        $sqlInsert = "INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) 
                      VALUES (:usuario_id, :nombres, :apellido_paterno, :apellido_materno, :dni, :fecha_nacimiento, :sexo, :direccion, :telefono)";
        
        $stmtInsert = $pdo->prepare($sqlInsert);
        $stmtInsert->bindParam(':usuario_id', $usuarioId);
        $stmtInsert->bindParam(':nombres', $nombres);
        $stmtInsert->bindParam(':apellido_paterno', $apellidoPaterno);
        $stmtInsert->bindParam(':apellido_materno', $apellidoMaterno);
        $stmtInsert->bindParam(':dni', $dni);
        $stmtInsert->bindParam(':fecha_nacimiento', $fechaNacimiento);
        $stmtInsert->bindParam(':sexo', $sexo);
        $stmtInsert->bindParam(':direccion', $direccion);
        $stmtInsert->bindParam(':telefono', $telefono);
        
        if ($stmtInsert->execute()) {
            echo "<p>✅ Registro creado exitosamente.</p>";
            $personaId = $pdo->lastInsertId();
            
            // Crear registro de administrador
            $sqlAdmin = "INSERT INTO administradores (persona_id, cargo, departamento, fecha_contratacion) 
                        VALUES (:persona_id, 'Directora General', 'Dirección Académica', '2020-01-15')";
            $stmtAdmin = $pdo->prepare($sqlAdmin);
            $stmtAdmin->bindParam(':persona_id', $personaId);
            
            if ($stmtAdmin->execute()) {
                echo "<p>✅ Registro de administrador creado exitosamente.</p>";
            } else {
                echo "<p>❌ Error al crear registro de administrador.</p>";
            }
        } else {
            echo "<p>❌ Error al crear registro en personas.</p>";
        }
        
    } else {
        echo "<h3>✅ Registro encontrado en tabla personas (ID: " . $persona['id'] . ")</h3>";
        
        // Actualizar el registro
        echo "<p>Actualizando registro...</p>";
        
        $sql = "UPDATE personas SET 
                    nombres = :nombres,
                    apellido_paterno = :apellido_paterno,
                    apellido_materno = :apellido_materno,
                    dni = :dni,
                    fecha_nacimiento = :fecha_nacimiento,
                    sexo = :sexo,
                    direccion = :direccion,
                    telefono = :telefono,
                    updated_at = CURRENT_TIMESTAMP
                WHERE usuario_id = :usuario_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':nombres', $nombres);
        $stmt->bindParam(':apellido_paterno', $apellidoPaterno);
        $stmt->bindParam(':apellido_materno', $apellidoMaterno);
        $stmt->bindParam(':dni', $dni);
        $stmt->bindParam(':fecha_nacimiento', $fechaNacimiento);
        $stmt->bindParam(':sexo', $sexo);
        $stmt->bindParam(':direccion', $direccion);
        $stmt->bindParam(':telefono', $telefono);
        $stmt->bindParam(':usuario_id', $usuarioId);

        $resultado = $stmt->execute();
        $filasAfectadas = $stmt->rowCount();
        
        echo "<p>Resultado: " . ($resultado ? 'Éxito' : 'Error') . "</p>";
        echo "<p>Filas afectadas: $filasAfectadas</p>";
        
        if ($resultado && $filasAfectadas > 0) {
            echo "<h3>✅ Actualización exitosa</h3>";
        } else {
            echo "<h3>❌ No se actualizó ninguna fila</h3>";
            
            // Mostrar error si existe
            $errorInfo = $stmt->errorInfo();
            if ($errorInfo[0] !== '00000') {
                echo "<p>Error SQL: " . $errorInfo[2] . "</p>";
            }
        }
    }
    
    // Mostrar datos actuales
    echo "<hr>";
    echo "<h3>Datos actuales en la base de datos:</h3>";
    
    $sqlActual = "SELECT * FROM personas WHERE usuario_id = :usuario_id";
    $stmtActual = $pdo->prepare($sqlActual);
    $stmtActual->bindParam(':usuario_id', $usuarioId);
    $stmtActual->execute();
    $datosActuales = $stmtActual->fetch();
    
    if ($datosActuales) {
        echo "<ul>";
        foreach ($datosActuales as $campo => $valor) {
            if (!is_numeric($campo)) {
                echo "<li>$campo: $valor</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>No se encontraron datos.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='perfil_a.php'>Ir al perfil</a></p>";
echo "<p><a href='verificar_admin.php'>Verificar admin</a></p>";
?>
