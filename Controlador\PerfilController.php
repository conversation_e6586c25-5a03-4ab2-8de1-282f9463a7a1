<?php
session_start();
require_once '../Modelo/Usuario.php';
require_once '../Modelo/Administrador.php';
require_once '../Modelo/Conexion.php';
require_once 'AuthController.php';

/**
 * Controlador para manejar las actualizaciones del perfil
 */
class PerfilController {
    private $pdo;
    private $administrador;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->administrador = new Administrador();
    }

    /**
     * Actualiza la información personal del administrador
     */
    public function actualizarInformacionPersonal() {
        // Verificar que el usuario esté autenticado y sea administrador
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('administrador')) {
            $this->enviarRespuesta(false, 'No tienes permisos para realizar esta acción.');
            return;
        }

        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();

            // Log para debugging
            error_log("Usuario actual ID: " . $usuarioActual['id']);
            error_log("POST data: " . print_r($_POST, true));

            // Obtener datos del formulario
            $nombres = trim($_POST['nombres'] ?? '');
            $apellidoPaterno = trim($_POST['apellido_paterno'] ?? '');
            $apellidoMaterno = trim($_POST['apellido_materno'] ?? '');
            $dni = trim($_POST['dni'] ?? '');
            $fechaNacimiento = trim($_POST['fecha_nacimiento'] ?? '');
            $sexo = trim($_POST['sexo'] ?? '');
            $direccion = trim($_POST['direccion'] ?? '');
            $telefono = trim($_POST['telefono'] ?? '');

            // Manejar fecha vacía
            if (empty($fechaNacimiento)) {
                $fechaNacimiento = null;
            }

            // Log de datos recibidos
            error_log("Datos recibidos - Nombres: $nombres, Apellido P: $apellidoPaterno, Apellido M: $apellidoMaterno");

            // Validaciones básicas
            if (empty($nombres) || empty($apellidoPaterno) || empty($apellidoMaterno)) {
                $this->enviarRespuesta(false, 'Los nombres y apellidos son obligatorios.');
                return;
            }

            if (!empty($dni) && !preg_match('/^\d{8}$/', $dni)) {
                $this->enviarRespuesta(false, 'El DNI debe tener exactamente 8 dígitos.');
                return;
            }

            // Verificar que el usuario existe en la tabla personas
            $sqlCheck = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtCheck->execute();
            $personaExiste = $stmtCheck->fetch();

            if (!$personaExiste) {
                error_log("No se encontró persona para usuario_id: " . $usuarioActual['id']);
                $this->enviarRespuesta(false, 'No se encontró la información personal del usuario.');
                return;
            }

            // Actualizar información personal
            $sql = "UPDATE personas SET
                        nombres = :nombres,
                        apellido_paterno = :apellido_paterno,
                        apellido_materno = :apellido_materno,
                        dni = :dni,
                        fecha_nacimiento = :fecha_nacimiento,
                        sexo = :sexo,
                        direccion = :direccion,
                        telefono = :telefono,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE usuario_id = :usuario_id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':nombres', $nombres);
            $stmt->bindParam(':apellido_paterno', $apellidoPaterno);
            $stmt->bindParam(':apellido_materno', $apellidoMaterno);
            $stmt->bindValue(':dni', empty($dni) ? null : $dni);
            $stmt->bindValue(':fecha_nacimiento', $fechaNacimiento);
            $stmt->bindParam(':sexo', $sexo);
            $stmt->bindValue(':direccion', empty($direccion) ? null : $direccion);
            $stmt->bindValue(':telefono', empty($telefono) ? null : $telefono);
            $stmt->bindParam(':usuario_id', $usuarioActual['id']);

            $resultado = $stmt->execute();
            $filasAfectadas = $stmt->rowCount();

            error_log("Resultado de UPDATE: " . ($resultado ? 'true' : 'false'));
            error_log("Filas afectadas: " . $filasAfectadas);

            if ($resultado && $filasAfectadas > 0) {
                // Actualizar datos en la sesión
                $_SESSION['nombres'] = $nombres;
                $_SESSION['apellido_paterno'] = $apellidoPaterno;
                $_SESSION['apellido_materno'] = $apellidoMaterno;

                $this->enviarRespuesta(true, 'Información personal actualizada correctamente.');
            } else {
                error_log("No se actualizó ninguna fila o falló la ejecución");
                $this->enviarRespuesta(false, 'Error al actualizar la información personal.');
            }

        } catch (Exception $e) {
            error_log("Error actualizando información personal: " . $e->getMessage());
            $this->enviarRespuesta(false, 'Error interno del servidor: ' . $e->getMessage());
        }
    }

    /**
     * Actualiza la información de cuenta del administrador
     */
    public function actualizarInformacionCuenta() {
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('administrador')) {
            $this->enviarRespuesta(false, 'No tienes permisos para realizar esta acción.');
            return;
        }

        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $nombreUsuario = trim($_POST['nombre_usuario'] ?? '');
            $email = trim($_POST['email'] ?? '');

            // Validaciones
            if (empty($nombreUsuario) || empty($email)) {
                $this->enviarRespuesta(false, 'El nombre de usuario y email son obligatorios.');
                return;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->enviarRespuesta(false, 'El formato del email no es válido.');
                return;
            }

            // Verificar que el nombre de usuario no esté en uso por otro usuario
            $sqlCheck = "SELECT id FROM usuarios WHERE nombre_usuario = :nombre_usuario AND id != :usuario_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->bindParam(':nombre_usuario', $nombreUsuario);
            $stmtCheck->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtCheck->execute();

            if ($stmtCheck->rowCount() > 0) {
                $this->enviarRespuesta(false, 'El nombre de usuario ya está en uso.');
                return;
            }

            // Verificar que el email no esté en uso por otro usuario
            $sqlCheckEmail = "SELECT id FROM usuarios WHERE email = :email AND id != :usuario_id";
            $stmtCheckEmail = $this->pdo->prepare($sqlCheckEmail);
            $stmtCheckEmail->bindParam(':email', $email);
            $stmtCheckEmail->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtCheckEmail->execute();

            if ($stmtCheckEmail->rowCount() > 0) {
                $this->enviarRespuesta(false, 'El email ya está en uso.');
                return;
            }

            // Actualizar información de cuenta
            $sql = "UPDATE usuarios SET 
                        nombre_usuario = :nombre_usuario,
                        email = :email,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :usuario_id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':nombre_usuario', $nombreUsuario);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':usuario_id', $usuarioActual['id']);

            if ($stmt->execute()) {
                // Actualizar datos en la sesión
                $_SESSION['nombre_usuario'] = $nombreUsuario;
                $_SESSION['email'] = $email;

                $this->enviarRespuesta(true, 'Información de cuenta actualizada correctamente.');
            } else {
                $this->enviarRespuesta(false, 'Error al actualizar la información de cuenta.');
            }

        } catch (Exception $e) {
            error_log("Error actualizando información de cuenta: " . $e->getMessage());
            $this->enviarRespuesta(false, 'Error interno del servidor.');
        }
    }

    /**
     * Actualiza la información profesional del administrador
     */
    public function actualizarInformacionProfesional() {
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('administrador')) {
            $this->enviarRespuesta(false, 'No tienes permisos para realizar esta acción.');
            return;
        }

        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $cargo = trim($_POST['cargo'] ?? '');
            $departamento = trim($_POST['departamento'] ?? '');
            $fechaContratacion = trim($_POST['fecha_contratacion'] ?? '');

            // Validaciones
            if (empty($cargo)) {
                $this->enviarRespuesta(false, 'El cargo es obligatorio.');
                return;
            }

            // Obtener el persona_id del administrador
            $sqlPersonaId = "SELECT persona_id FROM administradores a 
                            INNER JOIN personas p ON a.persona_id = p.id 
                            WHERE p.usuario_id = :usuario_id";
            $stmtPersonaId = $this->pdo->prepare($sqlPersonaId);
            $stmtPersonaId->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtPersonaId->execute();
            $resultado = $stmtPersonaId->fetch();

            if (!$resultado) {
                $this->enviarRespuesta(false, 'No se encontró la información del administrador.');
                return;
            }

            // Actualizar información profesional
            $sql = "UPDATE administradores SET 
                        cargo = :cargo,
                        departamento = :departamento,
                        fecha_contratacion = :fecha_contratacion,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE persona_id = :persona_id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':cargo', $cargo);
            $stmt->bindParam(':departamento', $departamento);
            $stmt->bindParam(':fecha_contratacion', $fechaContratacion);
            $stmt->bindParam(':persona_id', $resultado['persona_id']);

            if ($stmt->execute()) {
                $this->enviarRespuesta(true, 'Información profesional actualizada correctamente.');
            } else {
                $this->enviarRespuesta(false, 'Error al actualizar la información profesional.');
            }

        } catch (Exception $e) {
            error_log("Error actualizando información profesional: " . $e->getMessage());
            $this->enviarRespuesta(false, 'Error interno del servidor.');
        }
    }

    /**
     * Actualiza el contacto de emergencia
     */
    public function actualizarContactoEmergencia() {
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('administrador')) {
            $this->enviarRespuesta(false, 'No tienes permisos para realizar esta acción.');
            return;
        }

        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $nombreContacto = trim($_POST['nombre_contacto'] ?? '');
            $telefonoPrincipal = trim($_POST['telefono_principal'] ?? '');
            $telefonoAlternativo = trim($_POST['telefono_alternativo'] ?? '');
            $email = trim($_POST['email'] ?? '');

            // Validaciones
            if (empty($nombreContacto) || empty($telefonoPrincipal)) {
                $this->enviarRespuesta(false, 'El nombre del contacto y teléfono principal son obligatorios.');
                return;
            }

            // Obtener el persona_id del administrador
            $sqlPersonaId = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
            $stmtPersonaId = $this->pdo->prepare($sqlPersonaId);
            $stmtPersonaId->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtPersonaId->execute();
            $resultado = $stmtPersonaId->fetch();

            if (!$resultado) {
                $this->enviarRespuesta(false, 'No se encontró la información personal.');
                return;
            }

            $personaId = $resultado['id'];

            // Verificar si ya existe un contacto de emergencia
            $sqlCheck = "SELECT id FROM contactos_emergencia WHERE persona_id = :persona_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->bindParam(':persona_id', $personaId);
            $stmtCheck->execute();
            $existeContacto = $stmtCheck->fetch();

            if ($existeContacto) {
                // Actualizar contacto existente
                $sql = "UPDATE contactos_emergencia SET 
                            nombre_contacto = :nombre_contacto,
                            telefono_principal = :telefono_principal,
                            telefono_alternativo = :telefono_alternativo,
                            email = :email,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE persona_id = :persona_id";
            } else {
                // Crear nuevo contacto
                $sql = "INSERT INTO contactos_emergencia 
                        (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id) 
                        VALUES (:persona_id, :nombre_contacto, :telefono_principal, :telefono_alternativo, :email, 5)";
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':persona_id', $personaId);
            $stmt->bindParam(':nombre_contacto', $nombreContacto);
            $stmt->bindParam(':telefono_principal', $telefonoPrincipal);
            $stmt->bindParam(':telefono_alternativo', $telefonoAlternativo);
            $stmt->bindParam(':email', $email);

            if ($stmt->execute()) {
                $this->enviarRespuesta(true, 'Contacto de emergencia actualizado correctamente.');
            } else {
                $this->enviarRespuesta(false, 'Error al actualizar el contacto de emergencia.');
            }

        } catch (Exception $e) {
            error_log("Error actualizando contacto de emergencia: " . $e->getMessage());
            $this->enviarRespuesta(false, 'Error interno del servidor.');
        }
    }

    /**
     * Cambia la contraseña del administrador
     */
    public function cambiarPassword() {
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('administrador')) {
            $this->enviarRespuesta(false, 'No tienes permisos para realizar esta acción.');
            return;
        }

        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();

            $passwordActual = trim($_POST['current_password'] ?? '');
            $passwordNuevo = trim($_POST['new_password'] ?? '');
            $passwordConfirmar = trim($_POST['confirm_password'] ?? '');

            // Validaciones
            if (empty($passwordActual) || empty($passwordNuevo) || empty($passwordConfirmar)) {
                $this->enviarRespuesta(false, 'Todos los campos son obligatorios.');
                return;
            }

            if ($passwordNuevo !== $passwordConfirmar) {
                $this->enviarRespuesta(false, 'Las contraseñas nuevas no coinciden.');
                return;
            }

            if (strlen($passwordNuevo) < 8) {
                $this->enviarRespuesta(false, 'La nueva contraseña debe tener al menos 8 caracteres.');
                return;
            }

            // Verificar contraseña actual
            $sqlPassword = "SELECT password FROM usuarios WHERE id = :usuario_id";
            $stmtPassword = $this->pdo->prepare($sqlPassword);
            $stmtPassword->bindParam(':usuario_id', $usuarioActual['id']);
            $stmtPassword->execute();
            $resultado = $stmtPassword->fetch();

            if (!$resultado || !password_verify($passwordActual, $resultado['password'])) {
                $this->enviarRespuesta(false, 'La contraseña actual es incorrecta.');
                return;
            }

            // Actualizar contraseña
            $passwordHash = password_hash($passwordNuevo, PASSWORD_DEFAULT);
            $sql = "UPDATE usuarios SET
                        password = :password,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :usuario_id";

            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':password', $passwordHash);
            $stmt->bindParam(':usuario_id', $usuarioActual['id']);

            if ($stmt->execute()) {
                $this->enviarRespuesta(true, 'Contraseña actualizada correctamente.');
            } else {
                $this->enviarRespuesta(false, 'Error al actualizar la contraseña.');
            }

        } catch (Exception $e) {
            error_log("Error cambiando contraseña: " . $e->getMessage());
            $this->enviarRespuesta(false, 'Error interno del servidor.');
        }
    }

    /**
     * Envía una respuesta JSON
     */
    private function enviarRespuesta($exito, $mensaje) {
        header('Content-Type: application/json');
        echo json_encode([
            'exito' => $exito,
            'mensaje' => $mensaje
        ]);
        exit();
    }
}

// Procesar la acción solicitada
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $controller = new PerfilController();
    
    switch ($_POST['action']) {
        case 'actualizar_personal':
            $controller->actualizarInformacionPersonal();
            break;
        case 'actualizar_cuenta':
            $controller->actualizarInformacionCuenta();
            break;
        case 'actualizar_profesional':
            $controller->actualizarInformacionProfesional();
            break;
        case 'actualizar_emergencia':
            $controller->actualizarContactoEmergencia();
            break;
        case 'cambiar_password':
            $controller->cambiarPassword();
            break;
        default:
            header('Content-Type: application/json');
            echo json_encode(['exito' => false, 'mensaje' => 'Acción no válida.']);
            break;
    }
} else {
    header('Location: ../Vista/perfil_a.php');
    exit();
}
?>
