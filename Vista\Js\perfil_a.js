document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn")
    const changePasswordBtn = document.querySelector(".change-password-btn")
    const changeAvatarBtn = document.querySelector(".change-avatar-btn")
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn")
    const modals = document.querySelectorAll(".modal-overlay")
    const viewMoreBtn = document.querySelector(".view-more-btn")
    
    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const section = button.getAttribute("data-section")
        const modal = document.getElementById(`edit-${section}-modal`)
        if (modal) {
          modal.classList.add("active")
        }
      })
    })
    
    // Mostrar modal de cambio de contraseña
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener("click", () => {
        const modal = document.getElementById("change-password-modal")
        if (modal) {
          modal.classList.add("active")
        }
      })
    }
    
    // Simular cambio de avatar
    if (changeAvatarBtn) {
      changeAvatarBtn.addEventListener("click", () => {
        // Aquí se podría abrir un selector de archivos
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = "image/*"
        fileInput.style.display = "none"
    
        fileInput.addEventListener("change", (e) => {
          if (e.target.files && e.target.files[0]) {
            // Aquí se procesaría la imagen seleccionada
            // Por ahora solo mostramos un mensaje
            alert("Imagen seleccionada. En un entorno real, esta imagen se subiría al servidor.")
          }
        })
    
        document.body.appendChild(fileInput)
        fileInput.click()
        document.body.removeChild(fileInput)
      })
    }
    
    // Cerrar modales con botones de cierre
    modalCloseButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Manejar envío de formularios
    const forms = document.querySelectorAll(".edit-form")
    forms.forEach((form) => {
      form.addEventListener("submit", async (e) => {
        e.preventDefault()

        // Determinar qué tipo de formulario es
        const modal = form.closest(".modal-overlay")
        let action = ''

        if (modal.id === 'edit-personal-modal') {
          action = 'actualizar_personal'
        } else if (modal.id === 'edit-account-modal') {
          action = 'actualizar_cuenta'
        } else if (modal.id === 'edit-professional-modal') {
          action = 'actualizar_profesional'
        } else if (modal.id === 'edit-emergency-modal') {
          action = 'actualizar_emergencia'
        } else if (modal.id === 'change-password-modal') {
          action = 'cambiar_password'

          // Validación especial para cambio de contraseña
          const newPassword = form.querySelector('input[name="new_password"]').value
          const confirmPassword = form.querySelector('input[name="confirm_password"]').value

          if (newPassword !== confirmPassword) {
            mostrarMensaje('Las contraseñas nuevas no coinciden.', 'error')
            return
          }

          if (newPassword.length < 8) {
            mostrarMensaje('La nueva contraseña debe tener al menos 8 caracteres.', 'error')
            return
          }
        }

        // Crear FormData con los datos del formulario
        const formData = new FormData(form)
        formData.append('action', action)

        // Mostrar indicador de carga
        const submitBtn = form.querySelector('button[type="submit"]')
        const originalText = submitBtn.textContent
        submitBtn.textContent = 'Guardando...'
        submitBtn.disabled = true

        try {
          const response = await fetch('../Controlador/PerfilController.php', {
            method: 'POST',
            body: formData
          })

          const result = await response.json()

          if (result.exito) {
            // Mostrar mensaje de éxito
            mostrarMensaje(result.mensaje, 'success')

            // Cerrar modal
            modal.classList.remove("active")

            // Recargar la página para mostrar los cambios
            setTimeout(() => {
              window.location.reload()
            }, 1000)
          } else {
            // Mostrar mensaje de error
            mostrarMensaje(result.mensaje, 'error')
          }
        } catch (error) {
          console.error('Error:', error)
          mostrarMensaje('Error al guardar los cambios. Inténtalo de nuevo.', 'error')
        } finally {
          // Restaurar botón
          submitBtn.textContent = originalText
          submitBtn.disabled = false
        }
      })
    })

    // Función para mostrar mensajes
    function mostrarMensaje(mensaje, tipo) {
      // Crear elemento de mensaje
      const messageDiv = document.createElement('div')
      messageDiv.className = `message-toast ${tipo}`
      messageDiv.textContent = mensaje

      // Estilos del mensaje
      messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
      `

      if (tipo === 'success') {
        messageDiv.style.backgroundColor = '#4CAF50'
      } else {
        messageDiv.style.backgroundColor = '#f44336'
      }

      // Agregar al DOM
      document.body.appendChild(messageDiv)

      // Animar entrada
      setTimeout(() => {
        messageDiv.style.transform = 'translateX(0)'
      }, 100)

      // Remover después de 4 segundos
      setTimeout(() => {
        messageDiv.style.transform = 'translateX(100%)'
        setTimeout(() => {
          if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv)
          }
        }, 300)
      }, 4000)
    }
    
    // Simular carga de más actividad
    if (viewMoreBtn) {
      viewMoreBtn.addEventListener("click", () => {
        // Aquí se cargarían más elementos de actividad
        // Por ahora solo mostramos un mensaje
        alert("En un entorno real, se cargarían más elementos de actividad.")
      })
    }
    })
    