/* Estilos para la página de perfil del administrador */

:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    }
    
    /* Contenedor principal del perfil */
    .profile-container {
    max-width: 1000px;
    margin: 0 auto;
    }
    
    /* Sección principal del perfil */
    .profile-main {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow-sm);
    padding: 30px;
    margin-bottom: 30px;
    }
    
    .profile-header {
    display: flex;
    align-items: center;
    gap: 30px;
    }
    
    .profile-avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    }
    
    .profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-light);
    box-shadow: var(--shadow-md);
    }
    
    .profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    }
    
    .change-avatar-btn {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: var(--transition);
    }
    
    .change-avatar-btn:hover {
    background-color: var(--primary-color);
    color: white;
    }
    
    .profile-info {
    flex: 1;
    }
    
    .profile-info h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
    }
    
    .profile-role {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 5px;
    }
    
    .profile-grade {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 5px;
    }
    
    .profile-id {
    font-size: 0.9rem;
    color: var(--text-light);
    background-color: var(--secondary-color);
    display: inline-block;
    padding: 3px 10px;
    border-radius: 15px;
    margin-top: 5px;
    }
    
    /* Secciones del perfil */
    .profile-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
    overflow: hidden;
    }
    
    .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f9f9f9;
    border-bottom: 1px solid var(--border-color);
    }
    
    .section-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
    }
    
    .section-header h3 .material-icons {
    color: var(--primary-color);
    }
    
    .edit-section-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: var(--transition);
    }
    
    .edit-section-btn:hover {
    background-color: var(--primary-light);
    }
    
    .section-content {
    padding: 20px;
    }
    
    /* Grid de información */
    .profile-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    }
    
    .info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    }
    
    .info-label {
    font-size: 0.9rem;
    color: var(--text-light);
    }
    
    .info-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    }
    
    .password-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    }
    
    .change-password-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 3px 8px;
    border-radius: 5px;
    transition: var(--transition);
    }
    
    .change-password-btn:hover {
    background-color: var(--primary-light);
    }
    
    /* Permisos del sistema */
    .permissions-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    }
    
    .permission-group {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    }
    
    .permission-group h4 {
    font-size: 1rem;
    font-weight: 600;
    padding: 12px 15px;
    margin: 0;
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    }
    
    .permission-items {
    padding: 10px 15px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    }
    
    .permission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    }
    
    .permission-name {
    font-size: 0.95rem;
    color: var(--text-color);
    }
    
    .permission-status {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    }
    
    .permission-status.granted {
    color: var(--success-color);
    }
    
    .permission-status.denied {
    color: var(--danger-color);
    }
    
    /* Actividad reciente */
    .activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    }
    
    .activity-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    }
    
    .activity-item:hover {
    background-color: var(--secondary-color);
    }
    
    .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
    }
    
    .activity-details {
    flex: 1;
    }
    
    .activity-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
    }
    
    .activity-description {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
    }
    
    .activity-time {
    font-size: 0.8rem;
    color: var(--text-light);
    }
    
    .view-more-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.95rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 10px;
    margin: 10px auto 0;
    border-radius: 5px;
    transition: var(--transition);
    }
    
    .view-more-btn:hover {
    background-color: var(--primary-light);
    }
    
    /* Estilos para modales */
    .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    }
    
    .modal-overlay.active {
    opacity: 1;
    visibility: visible;
    }
    
    .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    transform: translateY(20px);
    transition: transform 0.3s;
    }
    
    .modal-overlay.active .modal-content {
    transform: translateY(0);
    }
    
    .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f9f9f9;
    }
    
    .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    }
    
    .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
    }
    
    .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
    }
    
    .modal-body {
    padding: 20px;
    }
    
    /* Estilos para formularios */
    .edit-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    }
    
    .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    }
    
    .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    }
    
    .form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    }
    
    .form-group input,
    .form-group select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    transition: var(--transition);
    }
    
    .form-group input:focus,
    .form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
    }
    
    .password-requirements {
    background-color: var(--secondary-color);
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
    }
    
    .password-requirements p {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: var(--text-color);
    }
    
    .password-requirements ul {
    padding-left: 20px;
    }
    
    .password-requirements li {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-bottom: 5px;
    }
    
    .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    }
    
    .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    }
    
    .btn-primary:hover {
    background-color: #1e40af;
    }
    
    .btn-secondary {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    }
    
    .btn-secondary:hover {
    background-color: var(--secondary-color);
    }
    
    /* Estilos para checkboxes de permisos */
    .permissions-edit-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    }
    
    .permission-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    }
    
    .permission-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    }
    
    /* Responsive */
    @media (max-width: 768px) {
    .profile-header {
      flex-direction: column;
      text-align: center;
    }
    
    .profile-info-grid {
      grid-template-columns: 1fr;
    }
    
    .form-row {
      grid-template-columns: 1fr;
      gap: 15px;
    }
    
    .permission-items {
      grid-template-columns: 1fr;
    }
    }

    /* Mensajes de notificación */
    .message-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      font-family: 'Poppins', sans-serif;
    }

    .message-toast.success {
      background-color: #4CAF50;
    }

    .message-toast.error {
      background-color: #f44336;
    }

    /* Indicador de carga en botones */
    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Mensaje cuando no hay datos */
    .no-data-message {
      text-align: center;
      padding: 40px 20px;
      color: #666;
    }

    .no-data-message p {
      margin-bottom: 20px;
      font-size: 16px;
    }

    .add-emergency-contact-btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .add-emergency-contact-btn:hover {
      background-color: var(--primary-dark);
    }

    /* Validación visual */
    .form-group input:valid {
      border-color: #4CAF50;
    }

    .form-group input[required]:invalid {
      border-color: #f44336;
    }