<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Mis Cursos</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/cursos_m.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Carlos García</h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mis Cursos</h1>
                    <p class="current-date">Lunes, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <!-- Filtro y acciones de cursos -->
                <section class="courses-actions">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" placeholder="Buscar cursos...">
                        </div>
                        <div class="filter-options">
                            <select id="period-filter">
                                <option value="all">Todos los periodos</option>
                                <option value="current" selected>Periodo actual</option>
                                <option value="past">Periodos anteriores</option>
                            </select>
                            <select id="grade-filter">
                                <option value="all">Todos los grados</option>
                                <option value="1">1° Primaria</option>
                                <option value="2">2° Primaria</option>
                                <option value="3">3° Primaria</option>
                                <option value="4">4° Primaria</option>
                                <option value="5">5° Primaria</option>
                                <option value="6">6° Primaria</option>
                            </select>
                        </div>
                    </div>
                    <button id="create-course-btn" class="create-course-btn">
                        <span class="material-icons">add</span>
                        Crear Curso
                    </button>
                </section>
                
                <!-- Lista de cursos -->
                <section class="courses-grid">
                    <!-- Curso 1 -->
                    <div class="course-card">
                        <div class="course-card-header">
                            <img src="/placeholder.svg?height=200&width=400" alt="Matemáticas">
                            <div class="course-icon">
                                <span class="material-icons">calculate</span>
                            </div>
                            <div class="course-actions">
                                <button class="course-action-btn edit-btn" data-id="1">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="course-action-btn delete-btn" data-id="1">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="course-card-body">
                            <h3>Matemáticas</h3>
                            <p class="course-grade">5° Primaria</p>
                            <div class="course-details">
                                <div class="course-detail">
                                    <span class="material-icons">schedule</span>
                                    <span>Lunes y Miércoles, 7:30 - 8:15</span>
                                </div>
                                <div class="course-detail">
                                    <span class="material-icons">groups</span>
                                    <span>25 estudiantes</span>
                                </div>
                            </div>
                        </div>
                        <div class="course-card-footer">
                            <a href="contenido_m.html" class="view-course-btn">
                                <span class="material-icons">visibility</span>
                                Ver curso
                            </a>
                        </div>
                    </div>
                    
                    <!-- Curso 2 -->
                    <div class="course-card">
                        <div class="course-card-header">
                            <img src="/placeholder.svg?height=200&width=400" alt="Matemáticas">
                            <div class="course-icon science-icon">
                                <span class="material-icons">science</span>
                            </div>
                            <div class="course-actions">
                                <button class="course-action-btn edit-btn" data-id="2">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="course-action-btn delete-btn" data-id="2">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="course-card-body">
                            <h3>Ciencias Naturales</h3>
                            <p class="course-grade">6° Primaria</p>
                            <div class="course-details">
                                <div class="course-detail">
                                    <span class="material-icons">schedule</span>
                                    <span>Martes y Jueves, 10:30 - 12:00</span>
                                </div>
                                <div class="course-detail">
                                    <span class="material-icons">groups</span>
                                    <span>28 estudiantes</span>
                                </div>
                            </div>
                        </div>
                        <div class="course-card-footer">
                            <a href="contenido_m.html" class="view-course-btn">
                                <span class="material-icons">visibility</span>
                                Ver curso
                            </a>
                        </div>
                    </div>
                    
                    <!-- Curso 3 -->
                    <div class="course-card">
                        <div class="course-card-header">
                            <img src="/placeholder.svg?height=200&width=400" alt="Matemáticas">
                            <div class="course-icon math-icon">
                                <span class="material-icons">calculate</span>
                            </div>
                            <div class="course-actions">
                                <button class="course-action-btn edit-btn" data-id="3">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="course-action-btn delete-btn" data-id="3">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="course-card-body">
                            <h3>Matemáticas</h3>
                            <p class="course-grade">4° Primaria</p>
                            <div class="course-details">
                                <div class="course-detail">
                                    <span class="material-icons">schedule</span>
                                    <span>Lunes y Viernes, 13:00 - 14:30</span>
                                </div>
                                <div class="course-detail">
                                    <span class="material-icons">groups</span>
                                    <span>22 estudiantes</span>
                                </div>
                            </div>
                        </div>
                        <div class="course-card-footer">
                            <a href="contenido_m.html" class="view-course-btn">
                                <span class="material-icons">visibility</span>
                                Ver curso
                            </a>
                        </div>
                    </div>
                    
                    <!-- Curso 4 -->
                    <div class="course-card">
                        <div class="course-card-header">
                            <img src="/placeholder.svg?height=200&width=400" alt="Matemáticas">
                            <div class="course-icon science-icon">
                                <span class="material-icons">science</span>
                            </div>
                            <div class="course-actions">
                                <button class="course-action-btn edit-btn" data-id="4">
                                    <span class="material-icons">edit</span>
                                </button>
                                <button class="course-action-btn delete-btn" data-id="4">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="course-card-body">
                            <h3>Ciencias Naturales</h3>
                            <p class="course-grade">3° Primaria</p>
                            <div class="course-details">
                                <div class="course-detail">
                                    <span class="material-icons">schedule</span>
                                    <span>Miércoles, 8:15 - 9:45</span>
                                </div>
                                <div class="course-detail">
                                    <span class="material-icons">groups</span>
                                    <span>26 estudiantes</span>
                                </div>
                            </div>
                        </div>
                        <div class="course-card-footer">
                            <a href="contenido_m.html" class="view-course-btn">
                                <span class="material-icons">visibility</span>
                                Ver curso
                            </a>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para crear/editar curso -->
    <div id="course-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Crear Nuevo Curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="course-form">
                    <input type="hidden" id="course-id" value="">
                    
                    <div class="form-group">
                        <label for="course-name">Nombre del Curso</label>
                        <input type="text" id="course-name" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-grade">Grado</label>
                            <select id="course-grade" required>
                                <option value="">Seleccionar grado</option>
                                <option value="1">1° Primaria</option>
                                <option value="2">2° Primaria</option>
                                <option value="3">3° Primaria</option>
                                <option value="4">4° Primaria</option>
                                <option value="5">5° Primaria</option>
                                <option value="6">6° Primaria</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="course-description">Descripción</label>
                        <textarea id="course-description" rows="4"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>Horario</label>
                        <div class="schedule-inputs">
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="lunes"> Lunes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-lunes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-lunes" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="martes"> Martes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-martes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-martes" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="miercoles"> Miércoles
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-miercoles" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-miercoles" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="jueves"> Jueves
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-jueves" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-jueves" disabled>
                                </div>
                            </div>
                            <div class="schedule-day">
                                <label>
                                    <input type="checkbox" name="schedule-day" value="viernes"> Viernes
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-viernes" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-viernes" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="course-icon">Ícono del Curso</label>
                        <select id="course-icon" required>
                            <option value="calculate">Matemáticas</option>
                            <option value="science">Ciencias</option>
                            <option value="menu_book">Lenguaje</option>
                            <option value="language">Inglés</option>
                            <option value="history_edu">Historia</option>
                            <option value="brush">Arte</option>
                            <option value="sports_soccer">Educación Física</option>
                            <option value="music_note">Música</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="course-image">Imagen del Curso (opcional)</label>
                        <div class="file-upload">
                            <input type="file" id="course-image" accept="image/*">
                            <div class="file-upload-btn">
                                <span class="material-icons">image</span>
                                Seleccionar imagen
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Curso</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal de confirmación para eliminar curso -->
    <div id="delete-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3>Eliminar Curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <p>¿Está seguro que desea eliminar este curso? Esta acción no se puede deshacer.</p>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-btn">Eliminar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/cursos_m.js"></script>
</body>
</html>

