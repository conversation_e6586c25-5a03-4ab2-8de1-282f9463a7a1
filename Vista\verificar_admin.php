<?php
require_once '../Modelo/Conexion.php';

try {
    $pdo = Conexion::getConexion();
    
    echo "<h2>Verificación de datos del administrador</h2>";
    
    // Verificar usuario admin
    $sqlUsuario = "SELECT * FROM usuarios WHERE nombre_usuario = 'admin'";
    $stmtUsuario = $pdo->prepare($sqlUsuario);
    $stmtUsuario->execute();
    $usuario = $stmtUsuario->fetch();
    
    if ($usuario) {
        echo "<h3>✅ Usuario encontrado:</h3>";
        echo "<ul>";
        echo "<li>ID: " . $usuario['id'] . "</li>";
        echo "<li>Usuario: " . $usuario['nombre_usuario'] . "</li>";
        echo "<li>Email: " . $usuario['email'] . "</li>";
        echo "<li>Rol: " . $usuario['rol'] . "</li>";
        echo "<li>Activo: " . ($usuario['activo'] ? 'Sí' : 'No') . "</li>";
        echo "</ul>";
        
        // Verificar persona
        $sqlPersona = "SELECT * FROM personas WHERE usuario_id = :usuario_id";
        $stmtPersona = $pdo->prepare($sqlPersona);
        $stmtPersona->bindParam(':usuario_id', $usuario['id']);
        $stmtPersona->execute();
        $persona = $stmtPersona->fetch();
        
        if ($persona) {
            echo "<h3>✅ Persona encontrada:</h3>";
            echo "<ul>";
            echo "<li>ID: " . $persona['id'] . "</li>";
            echo "<li>Nombres: " . $persona['nombres'] . "</li>";
            echo "<li>Apellido paterno: " . $persona['apellido_paterno'] . "</li>";
            echo "<li>Apellido materno: " . $persona['apellido_materno'] . "</li>";
            echo "<li>DNI: " . $persona['dni'] . "</li>";
            echo "<li>Fecha nacimiento: " . $persona['fecha_nacimiento'] . "</li>";
            echo "<li>Sexo: " . $persona['sexo'] . "</li>";
            echo "<li>Dirección: " . $persona['direccion'] . "</li>";
            echo "<li>Teléfono: " . $persona['telefono'] . "</li>";
            echo "</ul>";
            
            // Verificar administrador
            $sqlAdmin = "SELECT * FROM administradores WHERE persona_id = :persona_id";
            $stmtAdmin = $pdo->prepare($sqlAdmin);
            $stmtAdmin->bindParam(':persona_id', $persona['id']);
            $stmtAdmin->execute();
            $admin = $stmtAdmin->fetch();
            
            if ($admin) {
                echo "<h3>✅ Administrador encontrado:</h3>";
                echo "<ul>";
                echo "<li>ID: " . $admin['id'] . "</li>";
                echo "<li>Cargo: " . $admin['cargo'] . "</li>";
                echo "<li>Departamento: " . $admin['departamento'] . "</li>";
                echo "<li>Fecha contratación: " . $admin['fecha_contratacion'] . "</li>";
                echo "</ul>";
            } else {
                echo "<h3>❌ No se encontró registro en tabla administradores</h3>";
                
                // Crear registro de administrador
                echo "<p>Creando registro de administrador...</p>";
                $sqlInsertAdmin = "INSERT INTO administradores (persona_id, cargo, departamento, fecha_contratacion) 
                                  VALUES (:persona_id, 'Administrador General', 'Administración', '2025-01-01')";
                $stmtInsertAdmin = $pdo->prepare($sqlInsertAdmin);
                $stmtInsertAdmin->bindParam(':persona_id', $persona['id']);
                
                if ($stmtInsertAdmin->execute()) {
                    echo "<p>✅ Registro de administrador creado exitosamente.</p>";
                } else {
                    echo "<p>❌ Error al crear registro de administrador.</p>";
                }
            }
            
        } else {
            echo "<h3>❌ No se encontró registro en tabla personas</h3>";
            echo "<p>Esto es un problema. El usuario existe pero no tiene registro en personas.</p>";
        }
        
    } else {
        echo "<h3>❌ Usuario 'admin' no encontrado</h3>";
        echo "<p>Necesitas ejecutar el script de la base de datos primero.</p>";
    }
    
    // Mostrar estructura de tablas
    echo "<hr>";
    echo "<h3>Estructura de tabla personas:</h3>";
    $sqlDesc = "DESCRIBE personas";
    $stmtDesc = $pdo->prepare($sqlDesc);
    $stmtDesc->execute();
    $columns = $stmtDesc->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error de conexión:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='perfil_a.php'>Ir al perfil</a></p>";
echo "<p><a href='../Controlador/debug_perfil.php'>Ver debug del perfil</a></p>";
?>
