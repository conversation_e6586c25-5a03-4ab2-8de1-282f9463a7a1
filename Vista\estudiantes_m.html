<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Gestión de Estudiantes</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/estudiantes_m.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Carlos García</h3>
                        <p>Profesor</p>
                    </div>
                </div>

                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_m.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #2196f3;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_m.html" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1>Matemáticas</h1>
                            <p>5° Primaria</p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <div class="schedule-day">
                                <span class="day-label">Lunes</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                            <div class="schedule-day">
                                <span class="day-label">Miércoles</span>
                                <span class="day-time">7:30 - 8:15</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_m.html" class="course-tab">Contenido</a>
                    <a href="tareas_menu.html" class="course-tab">Tareas</a>
                    <a href="estudiantes_m.html" class="course-tab active">Estudiantes</a>
                    <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                    <a href="asistencias_estudiantes.html" class="course-tab">Asistencia</a>
                    <a href="mensajes_m.html" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de acciones y búsqueda -->
                <section class="filter-section">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="student-search" placeholder="Buscar estudiantes por nombre...">
                        </div>
                        <div class="filter-actions">
                            <button class="action-btn primary-btn" id="add-student-btn">
                                <span class="material-icons">person_add</span>
                                Agregar estudiante
                            </button>
                            <button class="action-btn secondary-btn" id="export-excel-btn">
                                <span class="material-icons">file_download</span>
                                Exportar Excel
                            </button>
                        </div>
                    </div>
                </section>
                
                <!-- Lista de estudiantes del curso -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Compañeros de clase</h2>
                        <span class="student-count">25 estudiantes</span>
                    </div>
                    
                    <div class="students-grid">
                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Juan Pérez</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Juan Pérez">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>María López</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="María López">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Pedro Gómez</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Pedro Gómez">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Ana Rodríguez</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Ana Rodríguez">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>

                        <div class="student-card enrolled">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=80&width=80" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h3>Lucía Vargas</h3>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <div class="student-actions">
                                <button class="student-action-btn delete-btn" title="Eliminar estudiante" data-student-name="Lucía Vargas">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pagination">
                        <button class="pagination-btn" disabled>
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modal para agregar estudiante -->
    <div id="add-student-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Agregar estudiante al curso</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="search-students-section">
                    <div class="search-box">
                        <span class="material-icons">search</span>
                        <input type="text" id="search-available-students" placeholder="Buscar estudiantes disponibles...">
                    </div>

                    <div class="available-students-list">
                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Pedro González Ruiz</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="025">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Isabella Moreno Castro</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="026">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Diego Herrera Vega</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="027">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Valentina Jiménez Ortiz</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="028">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Sebastián Ramírez Luna</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="029">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Carmen Silva Torres</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="030">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Andrés Mendoza Ruiz</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="031">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>

                        <div class="student-search-item">
                            <div class="student-avatar">
                                <img src="/placeholder.svg?height=60&width=60" alt="Foto de estudiante">
                            </div>
                            <div class="student-info">
                                <h4>Sofía Castillo Pérez</h4>
                                <p class="student-grade">5° Primaria</p>
                            </div>
                            <button class="add-student-btn" data-student-id="032">
                                <span class="material-icons">person_add</span>
                                Agregar
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancel-add-student">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmación para eliminar estudiante -->
    <div id="delete-student-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Eliminar estudiante</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="confirmation-message">
                    <span class="material-icons warning-icon">warning</span>
                    <p>¿Está seguro que desea eliminar a <strong id="student-to-delete-name">este estudiante</strong>?</p>
                    <p class="warning-text">Esta acción no se puede deshacer. Se eliminará permanentemente el registro del estudiante.</p>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancel-delete-student">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-student">Eliminar estudiante</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/estudiantes_m.js"></script>
