document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const createCourseBtn = document.getElementById("create-course-btn")
    const courseModal = document.getElementById("course-modal")
    const deleteModal = document.getElementById("delete-modal")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
    const courseForm = document.getElementById("course-form")
    const modalTitle = document.getElementById("modal-title")
    const courseIdInput = document.getElementById("course-id")
    const courseNameInput = document.getElementById("course-name")
    const courseGradeInput = document.getElementById("course-grade")
    const courseSectionInput = document.getElementById("course-section")
    const courseDescriptionInput = document.getElementById("course-description")
    const courseIconInput = document.getElementById("course-icon")
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn")
    const editBtns = document.querySelectorAll(".edit-btn")
    const deleteBtns = document.querySelectorAll(".delete-btn")
    const scheduleDayCheckboxes = document.querySelectorAll('input[name="schedule-day"]')
  
    // Actualizar fecha actual
    const dateElements = document.querySelectorAll(".current-date")
    if (dateElements.length > 0) {
      const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
      const today = new Date()
      dateElements.forEach((element) => {
        element.textContent = today.toLocaleDateString("es-ES", options)
      })
    }
  
    // Abrir modal para crear curso
    if (createCourseBtn && courseModal) {
      createCourseBtn.addEventListener("click", () => {
        // Resetear el formulario
        courseForm.reset()
        courseIdInput.value = ""
        modalTitle.textContent = "Crear Nuevo Curso"
  
        // Mostrar el modal
        courseModal.classList.add("active")
        document.body.style.overflow = "hidden" // Evitar scroll en el body
      })
    }
  
    // Cerrar modales
    if (modalCloseBtns.length > 0) {
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          // Cerrar todos los modales
          const modals = document.querySelectorAll(".modal-overlay")
          modals.forEach((modal) => {
            modal.classList.remove("active")
          })
          document.body.style.overflow = "" // Restaurar scroll en el body
        })
      })
    }
  
    // Cerrar modales al hacer clic fuera del contenido
    const modals = document.querySelectorAll(".modal-overlay")
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
      })
    })
  
    // Habilitar/deshabilitar campos de horario según checkbox
    if (scheduleDayCheckboxes.length > 0) {
      scheduleDayCheckboxes.forEach((checkbox) => {
        checkbox.addEventListener("change", () => {
          const day = checkbox.value
          const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
          const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
  
          if (startTimeInput && endTimeInput) {
            startTimeInput.disabled = !checkbox.checked
            endTimeInput.disabled = !checkbox.checked
  
            if (checkbox.checked) {
              startTimeInput.required = true
              endTimeInput.required = true
            } else {
              startTimeInput.required = false
              endTimeInput.required = false
            }
          }
        })
      })
    }
  
    // Abrir modal para editar curso
    console.log("Edit buttons found:", editBtns.length)
    if (editBtns.length > 0) {
      editBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
          e.stopPropagation()
          console.log("Edit button clicked")
          const courseId = btn.getAttribute("data-id")
          console.log("Course ID:", courseId)
  
          // En una implementación real, aquí se cargarían los datos del curso desde el servidor
          // Para esta demostración, usamos datos de ejemplo
          const courseData = {
            id: courseId,
            name: courseId === "1" ? "Matemáticas" : courseId === "2" ? "Ciencias Naturales" : "Matemáticas",
            grade: courseId === "1" ? "5" : courseId === "2" ? "6" : "4",
            section: courseId === "1" ? "A" : courseId === "2" ? "B" : "C",
            description: "Descripción del curso...",
            icon: courseId === "1" || courseId === "3" ? "calculate" : "science",
            schedule: {
              lunes: courseId === "1" || courseId === "3" ? true : false,
              martes: courseId === "2" ? true : false,
              miercoles: courseId === "1" || courseId === "4" ? true : false,
              jueves: courseId === "2" ? true : false,
              viernes: courseId === "3" ? true : false,
            },
            times: {
              lunes: { start: "07:30", end: "08:15" },
              martes: { start: "10:30", end: "12:00" },
              miercoles: { start: "08:15", end: "09:45" },
              jueves: { start: "10:30", end: "12:00" },
              viernes: { start: "13:00", end: "14:30" },
            },
          }
  
          // Llenar el formulario con los datos del curso
          courseIdInput.value = courseData.id
          courseNameInput.value = courseData.name
          courseGradeInput.value = courseData.grade
          courseSectionInput.value = courseData.section
          courseDescriptionInput.value = courseData.description
          courseIconInput.value = courseData.icon
  
          // Marcar los días de la semana y establecer horarios
          scheduleDayCheckboxes.forEach((checkbox) => {
            const day = checkbox.value
            checkbox.checked = courseData.schedule[day] || false
  
            const startTimeInput = document.querySelector(`input[name="start-time-${day}"]`)
            const endTimeInput = document.querySelector(`input[name="end-time-${day}"]`)
  
            if (startTimeInput && endTimeInput) {
              startTimeInput.disabled = !checkbox.checked
              endTimeInput.disabled = !checkbox.checked
  
              if (checkbox.checked) {
                startTimeInput.value = courseData.times[day].start
                endTimeInput.value = courseData.times[day].end
              }
            }
          })
  
          // Actualizar título del modal
          modalTitle.textContent = "Editar Curso"
  
          // Mostrar el modal
          courseModal.classList.add("active")
          document.body.style.overflow = "hidden" // Evitar scroll en el body
        })
      })
    }
  
    // Abrir modal para eliminar curso
    if (deleteBtns.length > 0) {
      deleteBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
          e.stopPropagation()
          const courseId = btn.getAttribute("data-id")
  
          // Guardar el ID del curso a eliminar
          if (confirmDeleteBtn) {
            confirmDeleteBtn.setAttribute("data-id", courseId)
          }
  
          // Mostrar el modal de confirmación
          if (deleteModal) {
            deleteModal.classList.add("active")
            document.body.style.overflow = "hidden" // Evitar scroll en el body
          }
        })
      })
    }
  
    // Confirmar eliminación de curso
    if (confirmDeleteBtn) {
      confirmDeleteBtn.addEventListener("click", () => {
        const courseId = confirmDeleteBtn.getAttribute("data-id")
  
        // En una implementación real, aquí se enviaría la solicitud al servidor
        console.log(`Eliminando curso con ID: ${courseId}`)
  
        // Cerrar el modal
        if (deleteModal) {
          deleteModal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
  
        // Mostrar mensaje de éxito
        alert(`Curso eliminado correctamente.`)
  
        // En una implementación real, aquí se eliminaría el curso del DOM o se recargaría la página
      })
    }
  
    // Enviar formulario de curso
    if (courseForm) {
      courseForm.addEventListener("submit", (e) => {
        e.preventDefault()
  
        const courseId = courseIdInput.value
        const isNewCourse = courseId === ""
  
        // En una implementación real, aquí se enviarían los datos al servidor
        console.log(`${isNewCourse ? "Creando" : "Actualizando"} curso:`, {
          id: courseId,
          name: courseNameInput.value,
          grade: courseGradeInput.value,
          section: courseSectionInput.value,
          description: courseDescriptionInput.value,
          icon: courseIconInput.value,
          // Aquí se recopilarían los datos de horario
        })
  
        // Cerrar el modal
        if (courseModal) {
          courseModal.classList.remove("active")
          document.body.style.overflow = "" // Restaurar scroll en el body
        }
  
        // Mostrar mensaje de éxito
        alert(`Curso ${isNewCourse ? "creado" : "actualizado"} correctamente.`)
  
        // En una implementación real, aquí se actualizaría la lista de cursos o se recargaría la página
      })
    }
  })
  