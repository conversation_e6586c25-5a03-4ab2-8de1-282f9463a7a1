<?php
// Script temporal para actualizar datos del administrador
// Ejecutar una sola vez para agregar datos de ejemplo

require_once '../Modelo/Conexion.php';

try {
    $pdo = Conexion::getConexion();
    
    echo "<h2>Actualizando datos del administrador...</h2>";
    
    // Actualizar información personal del administrador
    $sql1 = "UPDATE personas 
             SET 
                 nombres = 'Patricia <PERSON>',
                 apellido_paterno = 'Molina',
                 apellido_materno = 'Aguirre',
                 dni = '12345678',
                 fecha_nacimiento = '1980-05-15',
                 edad = 44,
                 sexo = 'femenino',
                 direccion = 'Av. Educación 123, San Isidro, Lima',
                 telefono = '991663041'
             WHERE usuario_id = 1";
    
    $stmt1 = $pdo->prepare($sql1);
    if ($stmt1->execute()) {
        echo "<p>✅ Información personal actualizada.</p>";
    } else {
        echo "<p>❌ Error actualizando información personal.</p>";
    }
    
    // Actualizar información del administrador
    $sql2 = "UPDATE administradores 
             SET 
                 cargo = 'Directora General',
                 departamento = 'Dirección Académica',
                 fecha_contratacion = '2020-01-15'
             WHERE persona_id = 1";
    
    $stmt2 = $pdo->prepare($sql2);
    if ($stmt2->execute()) {
        echo "<p>✅ Información profesional actualizada.</p>";
    } else {
        echo "<p>❌ Error actualizando información profesional.</p>";
    }
    
    // Verificar si ya existe un contacto de emergencia
    $sqlCheck = "SELECT id FROM contactos_emergencia WHERE persona_id = 1";
    $stmtCheck = $pdo->prepare($sqlCheck);
    $stmtCheck->execute();
    $existeContacto = $stmtCheck->fetch();
    
    if ($existeContacto) {
        // Actualizar contacto existente
        $sql3 = "UPDATE contactos_emergencia 
                 SET 
                     nombre_contacto = 'Carlos Molina (Esposo)',
                     telefono_principal = '987654321',
                     telefono_alternativo = '991234567',
                     email = '<EMAIL>',
                     tipo_contacto_id = 5
                 WHERE persona_id = 1";
        echo "<p>✅ Contacto de emergencia actualizado.</p>";
    } else {
        // Insertar nuevo contacto
        $sql3 = "INSERT INTO contactos_emergencia (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id)
                 VALUES (1, 'Carlos Molina (Esposo)', '987654321', '991234567', '<EMAIL>', 5)";
        echo "<p>✅ Contacto de emergencia creado.</p>";
    }
    
    $stmt3 = $pdo->prepare($sql3);
    $stmt3->execute();
    
    // Verificar los datos actualizados
    $sqlVerify = "SELECT 
                      u.nombre_usuario,
                      u.email,
                      u.rol,
                      p.nombres,
                      p.apellido_paterno,
                      p.apellido_materno,
                      p.dni,
                      p.fecha_nacimiento,
                      p.sexo,
                      p.direccion,
                      p.telefono,
                      a.cargo,
                      a.departamento,
                      a.fecha_contratacion
                  FROM usuarios u
                  INNER JOIN personas p ON u.id = p.usuario_id
                  INNER JOIN administradores a ON p.id = a.persona_id
                  WHERE u.nombre_usuario = 'admin'";
    
    $stmtVerify = $pdo->prepare($sqlVerify);
    $stmtVerify->execute();
    $admin = $stmtVerify->fetch();
    
    echo "<h3>Datos actualizados:</h3>";
    echo "<ul>";
    echo "<li><strong>Nombre:</strong> " . $admin['nombres'] . " " . $admin['apellido_paterno'] . " " . $admin['apellido_materno'] . "</li>";
    echo "<li><strong>DNI:</strong> " . $admin['dni'] . "</li>";
    echo "<li><strong>Email:</strong> " . $admin['email'] . "</li>";
    echo "<li><strong>Cargo:</strong> " . $admin['cargo'] . "</li>";
    echo "<li><strong>Departamento:</strong> " . $admin['departamento'] . "</li>";
    echo "<li><strong>Fecha de contratación:</strong> " . $admin['fecha_contratacion'] . "</li>";
    echo "</ul>";
    
    // Verificar contacto de emergencia
    $sqlContacto = "SELECT 
                        ce.nombre_contacto,
                        ce.telefono_principal,
                        ce.telefono_alternativo,
                        ce.email,
                        tce.nombre as tipo_contacto
                    FROM contactos_emergencia ce
                    LEFT JOIN tipos_contacto_emergencia tce ON ce.tipo_contacto_id = tce.id
                    WHERE ce.persona_id = 1";
    
    $stmtContacto = $pdo->prepare($sqlContacto);
    $stmtContacto->execute();
    $contacto = $stmtContacto->fetch();
    
    if ($contacto) {
        echo "<h3>Contacto de emergencia:</h3>";
        echo "<ul>";
        echo "<li><strong>Nombre:</strong> " . $contacto['nombre_contacto'] . "</li>";
        echo "<li><strong>Teléfono principal:</strong> " . $contacto['telefono_principal'] . "</li>";
        echo "<li><strong>Teléfono alternativo:</strong> " . $contacto['telefono_alternativo'] . "</li>";
        echo "<li><strong>Email:</strong> " . $contacto['email'] . "</li>";
        echo "</ul>";
    }
    
    echo "<h3>✅ ¡Datos actualizados exitosamente!</h3>";
    echo "<p><a href='perfil_a.php'>Ir al perfil del administrador</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error: " . $e->getMessage() . "</h3>";
}
?>
