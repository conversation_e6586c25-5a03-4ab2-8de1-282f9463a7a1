<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Contenido del Curso</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/maestros.css">
    <link rel="stylesheet" href="./Css/contenido_m.css">
</head>
<body class="has-course-header">
    <div class="plataforma-container has-course-header">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="/placeholder.svg?height=100&width=100" alt="Foto de perfil">
                    </div>
                    <div class="user-details">
                        <h3>Carlos García</h3>
                        <p>Profesor</p>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.html">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li>
                            <a href="perfil_m.html">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_m.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="intranet.html">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del curso -->
            <header class="course-header" style="background-color: #2196f3;">
                <div class="course-header-content">
                    <div class="course-header-left">
                        <a href="cursos_m.html" class="back-button">
                            <span class="material-icons">arrow_back</span>
                        </a>
                        <div class="course-title">
                            <h1>Matemáticas</h1>
                            <p>5° Primaria</p>
                        </div>
                    </div>
                    <div class="course-header-right">
                        <div class="course-schedule-info">
                            <div class="schedule-day">
                                <span class="day-label">Lunes</span>
                                <span class="day-time">8:00 - 9:30</span>
                            </div>
                            <div class="schedule-day">
                                <span class="day-label">Miércoles</span>
                                <span class="day-time">8:00 - 9:30</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Navegación del curso -->
            <div class="course-navigation">
                <div class="course-nav-tabs">
                    <a href="contenido_m.html" class="course-tab active">Contenido</a>
                    <a href="tareas_menu.html" class="course-tab">Tareas</a>
                    <a href="estudiantes_m.html" class="course-tab">Estudiantes</a>
                    <a href="calificaciones_m.html" class="course-tab">Calificaciones</a>
                    <a href="asistencias_estudiantes.html" class="course-tab">Asistencia</a>
                    <a href="mensajes_m.html" class="course-tab">Mensajes</a>
                </div>
            </div>
            
            <div class="content-body">
                <!-- Sección de videoconferencia -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Videoconferencia</h2>
                        <div class="section-actions">
                            <button class="action-btn create-meet-btn" style="display: none;">
                                <span class="material-icons">add</span>
                                Agregar enlace de videoconferencia
                            </button>
                        </div>
                    </div>

                    <div class="meet-section active-meet">
                        <div class="meet-info">
                            <div class="meet-icon">
                                <span class="material-icons">videocam</span>
                            </div>
                            <div class="meet-details">
                                <h3>Clase en vivo: Operaciones con fracciones</h3>
                                <p>Hoy, 22 de marzo - 8:00 AM</p>
                                <div class="meet-actions">
                                    <a href="https://zoom.us/j/1234567890?pwd=abc123" class="meet-link" target="_blank">
                                        <span class="material-icons">open_in_new</span>
                                        Unirse a la videoconferencia
                                    </a>
                                    <button class="meet-action-btn edit-btn">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="meet-action-btn delete-btn">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Contenido del curso -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Contenido del curso</h2>
                        <div class="section-actions">
                            <button class="action-btn create-folder-btn">
                                <span class="material-icons">create_new_folder</span>
                                Nueva carpeta
                            </button>
                            <button class="action-btn create-content-btn">
                                <span class="material-icons">add</span>
                                Nuevo contenido
                            </button>
                        </div>
                    </div>
                    
                    <div class="course-content">
                        <!-- Semana 1 -->
                        <div class="week-container">
                            <div class="week-header" data-week="1">
                                <div class="week-title">
                                    <h3>Semana 1: Introducción a las fracciones</h3>
                                    <span class="week-dates">15/03/2025 - 21/03/2025</span>
                                </div>
                                <div class="week-actions">
                                    <button class="week-action-btn edit-btn" data-id="1">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="week-action-btn delete-btn" data-id="1">
                                        <span class="material-icons">delete</span>
                                    </button>
                                    <span class="material-icons toggle-icon">expand_less</span>
                                </div>
                            </div>
                            <div class="folder-content" id="week-1-content" style="display: block;">
                                <div class="content-item">
                                    <div class="content-icon announcement">
                                        <span class="material-icons">campaign</span>
                                    </div>
                                    <div class="content-details" id="anuncio-bienvenida">
                                        <h4>Anuncio: Bienvenidos al curso de matemáticas</h4>
                                        <p>Información importante sobre el curso y objetivos</p>
                                        <span class="content-date">Publicado: 15/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <button class="content-action-btn edit-btn" data-id="1">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="1">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="content-item">
                                    <div class="content-icon ppt">
                                        <span class="material-icons">slideshow</span>
                                    </div>
                                    <div class="content-details" id="presentacion-fracciones">
                                        <h4>Presentación: Introducción a las fracciones</h4>
                                        <p>Conceptos básicos y representación gráfica</p>
                                        <span class="content-date">Publicado: 16/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <button class="content-action-btn edit-btn" data-id="2">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="2">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="content-item">
                                    <div class="content-icon pdf">
                                        <span class="material-icons">picture_as_pdf</span>
                                    </div>
                                    <div class="content-details" id="guia-fracciones">
                                        <h4>Documento: Guía de ejercicios</h4>
                                        <p>Ejercicios prácticos sobre fracciones</p>
                                        <span class="content-date">Publicado: 17/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <button class="content-action-btn edit-btn" data-id="3">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="3">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="content-item">
                                    <div class="content-icon task">
                                        <span class="material-icons">assignment</span>
                                    </div>
                                    <div class="content-details" id="tarea-sumas-restas">
                                        <h4>Tarea: Ejercicios de Fracciones</h4>
                                        <p>Realiza las sumas y restas de la pág. 19 - 20</p>
                                        <span class="content-date">Fecha límite: 18/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <span class="task-status">3 pendientes</span>
                                        <button class="content-action-btn edit-btn" data-id="4">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="4">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="content-item">
                                    <div class="content-icon link">
                                        <span class="material-icons">link</span>
                                    </div>
                                    <div class="content-details" id="video-fracciones">
                                        <h4>Enlace: Video explicativo sobre fracciones</h4>
                                        <p>Video complementario para reforzar conceptos</p>
                                        <span class="content-date">Publicado: 18/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <button class="content-action-btn edit-btn" data-id="5">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="5">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="content-item">
                                    <div class="content-icon video">
                                        <span class="material-icons">play_circle</span>
                                    </div>
                                    <div class="content-details" id="video-tutorial-fracciones">
                                        <h4>Video: Tutorial interactivo de fracciones</h4>
                                        <p>Tutorial con ejercicios interactivos y animaciones</p>
                                        <span class="content-date">Publicado: 19/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <button class="content-action-btn edit-btn" data-id="6">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="6">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="content-item">
                                    <div class="content-icon participation">
                                        <span class="material-icons">groups</span>
                                    </div>
                                    <div class="content-details" id="participacion-clase-fracciones">
                                        <h4>Participación: Discusión sobre fracciones en la vida cotidiana</h4>
                                        <p>Actividad de participación oral y evaluación de comprensión</p>
                                        <span class="content-date">Fecha de registro: 20/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <span class="participation-status">5 calificados</span>
                                        <button class="content-action-btn edit-btn" data-id="7">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="7">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="add-content-btn">
                                    <span class="material-icons">add_circle</span>
                                    <span>Agregar contenido a esta carpeta</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Semana 2 -->
                        <div class="week-container">
                            <div class="week-header" data-week="2">
                                <div class="week-title">
                                    <h3>Semana 2: Operaciones con fracciones</h3>
                                    <span class="week-dates">22/03/2025 - 28/03/2025</span>
                                </div>
                                <div class="week-actions">
                                    <button class="week-action-btn edit-btn" data-id="2">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="week-action-btn delete-btn" data-id="2">
                                        <span class="material-icons">delete</span>
                                    </button>
                                    <span class="material-icons toggle-icon">expand_more</span>
                                </div>
                            </div>
                            <div class="folder-content" id="week-2-content" style="display: none;">
                                <div class="content-item">
                                    <div class="content-icon task">
                                        <span class="material-icons">assignment</span>
                                    </div>
                                    <div class="content-details" id="tarea-multiplicacion">
                                        <h4>Tarea: Multiplicación de fracciones</h4>
                                        <p>Ejercicios de multiplicación de fracciones</p>
                                        <span class="content-date">Fecha límite: 25/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <span class="task-status">0 pendientes</span>
                                        <button class="content-action-btn edit-btn" data-id="8">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="8">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="content-item">
                                    <div class="content-icon exam">
                                        <span class="material-icons">quiz</span>
                                    </div>
                                    <div class="content-details" id="examen-fracciones">
                                        <h4>Examen: Evaluación de fracciones</h4>
                                        <p>Examen sobre conceptos básicos y operaciones con fracciones</p>
                                        <span class="content-date">Fecha límite: 28/03/2025</span>
                                    </div>
                                    <div class="content-actions">
                                        <span class="task-status">5 pendientes</span>
                                        <button class="content-action-btn edit-btn" data-id="9">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="content-action-btn delete-btn" data-id="9">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="add-content-btn">
                                    <span class="material-icons">add_circle</span>
                                    <span>Agregar contenido a esta carpeta</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Semana 3 (vacía) -->
                        <div class="week-container">
                            <div class="week-header" data-week="3">
                                <div class="week-title">
                                    <h3>Semana 3: División de fracciones</h3>
                                    <span class="week-dates">29/03/2025 - 04/04/2025</span>
                                </div>
                                <div class="week-actions">
                                    <button class="week-action-btn edit-btn" data-id="3">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="week-action-btn delete-btn" data-id="3">
                                        <span class="material-icons">delete</span>
                                    </button>
                                    <span class="material-icons toggle-icon">expand_more</span>
                                </div>
                            </div>
                            <div class="folder-content" id="week-3-content" style="display: none;">
                                <div class="empty-content">
                                    <span class="material-icons">inbox</span>
                                    <p>Aún no se ha subido ningún contenido</p>
                                </div>
                                <div class="add-content-btn">
                                    <span class="material-icons">add_circle</span>
                                    <span>Agregar contenido a esta carpeta</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modal para crear/editar carpeta -->
    <div id="folder-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="folder-modal-title">Crear Nueva Carpeta</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="folder-form">
                    <input type="hidden" id="folder-id" value="">
                    
                    <div class="form-group">
                        <label for="folder-title">Título de la carpeta</label>
                        <input type="text" id="folder-title" required placeholder="Ej: Semana 1: Introducción">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="folder-start-date">Fecha de inicio</label>
                            <input type="date" id="folder-start-date" required>
                        </div>
                        <div class="form-group">
                            <label for="folder-end-date">Fecha de fin</label>
                            <input type="date" id="folder-end-date" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="folder-description">Descripción (opcional)</label>
                        <textarea id="folder-description" rows="3" placeholder="Descripción breve de los contenidos de esta carpeta"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Carpeta</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para crear/editar contenido -->
    <div id="content-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="content-modal-title">Crear Nuevo Contenido</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="content-form">
                    <input type="hidden" id="content-id" value="">
                    
                    <div class="form-group">
                        <label for="content-type">Tipo de contenido</label>
                        <select id="content-type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="announcement">Anuncio</option>
                            <option value="task">Tarea</option>
                            <option value="exam">Examen</option>
                            <option value="document">Documento</option>
                            <option value="presentation">Presentación</option>
                            <option value="link">Enlace</option>
                            <option value="video">Video</option>
                            <option value="participation">Participación</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="content-section">Sección</label>
                        <select id="content-section" required>
                            <option value="">Seleccionar sección</option>
                            <option value="seccion-1">Sección 1: Introducción</option>
                            <option value="seccion-2">Sección 2: Desarrollo</option>
                            <option value="seccion-3">Sección 3: Práctica</option>
                            <option value="seccion-4">Sección 4: Evaluación</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="content-title">Título</label>
                        <input type="text" id="content-title" required placeholder="Ej: Tarea: Ejercicios de fracciones / Examen: Evaluación de fracciones">
                    </div>
                    
                    <div class="form-group">
                        <label for="content-description">Contenido Completo *</label>
                        <div class="rich-text-editor">
                            <div class="editor-toolbar">
                                <button type="button" class="toolbar-btn" data-command="bold" title="Negrita">
                                    <strong>B</strong>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="italic" title="Cursiva">
                                    <em>I</em>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="underline" title="Subrayado">
                                    <u>U</u>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="strikeThrough" title="Tachado">
                                    <s>T</s>
                                </button>
                                <div class="toolbar-separator"></div>
                                <button type="button" class="toolbar-btn" data-command="insertUnorderedList" title="Lista con viñetas">
                                    <span class="material-icons">format_list_bulleted</span>
                                </button>
                                <button type="button" class="toolbar-btn" data-command="insertOrderedList" title="Lista numerada">
                                    <span class="material-icons">format_list_numbered</span>
                                </button>
                                <div class="toolbar-separator"></div>
                                <button type="button" class="toolbar-btn" data-command="createLink" title="Insertar enlace">
                                    <span class="material-icons">link</span>
                                </button>
                                <button type="button" class="toolbar-btn" id="insert-image-btn" title="Insertar imagen">
                                    <span class="material-icons">image</span>
                                </button>
                            </div>
                            <div id="content-description" class="editor-content" contenteditable="true" placeholder="Escribe el contenido completo aquí..."></div>
                        </div>
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                    </div>
                    
                    <!-- Campos específicos para tareas y exámenes -->
                    <div id="task-fields" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="task-due-date">Fecha límite</label>
                                <input type="date" id="task-due-date">
                            </div>
                            <div class="form-group">
                                <label for="task-due-time">Hora límite</label>
                                <input type="time" id="task-due-time">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="task-points">Puntos</label>
                            <input type="number" id="task-points" min="0" max="100" value="10">
                        </div>
                        

                    </div>
                    
                    <!-- Campos específicos para documentos y presentaciones -->
                    <div id="file-fields" style="display: none;">
                        <div class="form-group">
                            <label for="content-file">Archivo</label>
                            <div class="file-upload">
                                <input type="file" id="content-file">
                                <div class="file-upload-btn">
                                    <span class="material-icons">upload_file</span>
                                    Seleccionar archivo
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Campos específicos para enlaces y videos -->
                    <div id="link-fields" style="display: none;">
                        <div class="form-group">
                            <label for="content-url">URL</label>
                            <input type="url" id="content-url" placeholder="https://...">
                        </div>
                    </div>

                    <!-- Campos específicos para participación -->
                    <div id="participation-fields" style="display: none;">
                        <div class="form-group">
                            <label>Lista de Estudiantes</label>
                            <div class="students-list">
                                <div class="student-item">
                                    <span class="student-name">Ana García López</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">Carlos Mendoza Silva</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">María Rodríguez Torres</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">José Fernández Cruz</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                                <div class="student-item">
                                    <span class="student-name">Lucía Vargas Morales</span>
                                    <input type="number" class="participation-grade" min="0" max="20" step="0.5" placeholder="Nota">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Contenido</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para crear/editar videoconferencia -->
    <div id="meet-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="meet-modal-title">Crear Videoconferencia</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="meet-form">
                    <input type="hidden" id="meet-id" value="">
                    
                    <div class="form-group">
                        <label for="meet-title">Título</label>
                        <input type="text" id="meet-title" required placeholder="Ej: Clase en vivo: Operaciones con fracciones">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="meet-date">Fecha</label>
                            <input type="date" id="meet-date" required>
                        </div>
                        <div class="form-group">
                            <label for="meet-time">Hora</label>
                            <input type="time" id="meet-time" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="meet-url">URL de la reunión</label>
                        <input type="url" id="meet-url" required placeholder="https://...">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar Videoconferencia</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal de confirmación para eliminar -->
    <div id="delete-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3 id="delete-modal-title">Eliminar Elemento</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="delete-modal-message">¿Está seguro que desea eliminar este elemento? Esta acción no se puede deshacer.</p>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="button" class="btn-danger" id="confirm-delete-btn">Eliminar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal para ver anuncio -->
    <div id="announcement-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Anuncio: Bienvenidos al curso de matemáticas</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="announcement-meta">
                    Publicado: 15/03/2025 por Prof. Carlos García
                </div>
                <div class="announcement-text">
                    <p>¡Bienvenidos al curso de matemáticas de 5° grado!</p>
                    <p>En este curso aprenderemos sobre fracciones, decimales, geometría y muchos temas más. Es importante que revisen el material de clase regularmente y completen las tareas asignadas.</p>
                    <p>Objetivos del curso:</p>
                    <ul>
                        <li>Comprender y aplicar operaciones con fracciones</li>
                        <li>Resolver problemas matemáticos de la vida cotidiana</li>
                        <li>Desarrollar habilidades de razonamiento lógico</li>
                        <li>Aprender conceptos básicos de geometría</li>
                    </ul>
                    <p>¡Espero que disfruten el curso y aprendan mucho!</p>
                </div>

            </div>
        </div>
    </div>

    <!-- Modal para vista previa de documentos -->
    <div id="document-preview-modal" class="modal-overlay">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 id="document-title">Vista Previa del Documento</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="document-preview-container">
                    <div class="document-info">
                        <div class="document-meta">
                            <span class="document-type">Documento PDF</span>
                            <span class="document-size">2.5 MB</span>
                            <span class="document-date">Publicado: 16/03/2025</span>
                        </div>
                    </div>
                    <div class="document-content">
                        <div class="document-description-enhanced">
                            <div class="description-content">
                                <p>Guía completa con ejercicios prácticos sobre fracciones organizados por nivel de dificultad. Incluye problemas de suma, resta, multiplicación y división de fracciones, con ejemplos resueltos paso a paso y espacios para que los estudiantes practiquen.</p>
                                <p><strong>Contenido incluido:</strong> Conceptos básicos, operaciones fundamentales, ejercicios resueltos y actividades prácticas. Formato descargable e imprimible.</p>
                            </div>
                        </div>

                        <div class="document-preview-card">
                            <div class="file-preview-header">
                                <div class="file-icon-container">
                                    <span class="material-icons file-icon pdf-icon">picture_as_pdf</span>
                                </div>
                                <div class="file-info">
                                    <h4 class="file-name">Guía de ejercicios fracciones.pdf</h4>
                                    <div class="file-details">
                                        <span class="file-size">2.5 MB</span>
                                        <span class="file-type">Documento PDF</span>
                                    </div>
                                </div>
                                <div class="file-actions">
                                    <button class="btn-download" onclick="downloadFile('guia_ejercicios_fracciones.pdf')">
                                        <span class="material-icons">download</span>
                                        Descargar
                                    </button>
                                </div>
                            </div>
                            <div class="file-preview-content">
                                <div class="preview-thumbnail">
                                    <span class="material-icons preview-icon">visibility</span>
                                    <p>Haz clic para ver el contenido del documento</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="btn-secondary" id="download-document-btn">
                            <span class="material-icons">download</span>
                            Descargar
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>



    <script src="./Js/plataforma.js"></script>
    <script src="./Js/contenido_m.js"></script>
</body>
</html>

