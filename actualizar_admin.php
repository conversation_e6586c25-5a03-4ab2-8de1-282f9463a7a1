<?php
require_once 'Modelo/Conexion.php';

try {
    $pdo = Conexion::getConexion();
    
    echo "Conectado a la base de datos...\n";
    
    // Actualizar información personal del administrador
    $sql1 = "UPDATE personas 
             SET 
                 nombres = '<PERSON>',
                 apellido_paterno = 'Molina',
                 apellido_materno = 'Aguirre',
                 dni = '12345678',
                 fecha_nacimiento = '1980-05-15',
                 edad = 44,
                 sexo = 'femenino',
                 direccion = 'Av. Educación 123, San Isidro, Lima',
                 telefono = '991663041'
             WHERE usuario_id = 1";
    
    $stmt1 = $pdo->prepare($sql1);
    $stmt1->execute();
    echo "Información personal actualizada.\n";
    
    // Actualizar información del administrador
    $sql2 = "UPDATE administradores 
             SET 
                 cargo = 'Directora General',
                 departamento = 'Dirección Académica',
                 fecha_contratacion = '2020-01-15'
             WHERE persona_id = 1";
    
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->execute();
    echo "Información profesional actualizada.\n";
    
    // Verificar si ya existe un contacto de emergencia
    $sqlCheck = "SELECT id FROM contactos_emergencia WHERE persona_id = 1";
    $stmtCheck = $pdo->prepare($sqlCheck);
    $stmtCheck->execute();
    $existeContacto = $stmtCheck->fetch();
    
    if ($existeContacto) {
        // Actualizar contacto existente
        $sql3 = "UPDATE contactos_emergencia 
                 SET 
                     nombre_contacto = 'Carlos Molina (Esposo)',
                     telefono_principal = '987654321',
                     telefono_alternativo = '991234567',
                     email = '<EMAIL>',
                     tipo_contacto_id = 5
                 WHERE persona_id = 1";
        echo "Contacto de emergencia actualizado.\n";
    } else {
        // Insertar nuevo contacto
        $sql3 = "INSERT INTO contactos_emergencia (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id)
                 VALUES (1, 'Carlos Molina (Esposo)', '987654321', '991234567', '<EMAIL>', 5)";
        echo "Contacto de emergencia creado.\n";
    }
    
    $stmt3 = $pdo->prepare($sql3);
    $stmt3->execute();
    
    // Verificar los datos actualizados
    $sqlVerify = "SELECT 
                      u.nombre_usuario,
                      u.email,
                      u.rol,
                      p.nombres,
                      p.apellido_paterno,
                      p.apellido_materno,
                      p.dni,
                      p.fecha_nacimiento,
                      p.sexo,
                      p.direccion,
                      p.telefono,
                      a.cargo,
                      a.departamento,
                      a.fecha_contratacion
                  FROM usuarios u
                  INNER JOIN personas p ON u.id = p.usuario_id
                  INNER JOIN administradores a ON p.id = a.persona_id
                  WHERE u.nombre_usuario = 'admin'";
    
    $stmtVerify = $pdo->prepare($sqlVerify);
    $stmtVerify->execute();
    $admin = $stmtVerify->fetch();
    
    echo "\n=== DATOS ACTUALIZADOS ===\n";
    echo "Nombre: " . $admin['nombres'] . " " . $admin['apellido_paterno'] . " " . $admin['apellido_materno'] . "\n";
    echo "DNI: " . $admin['dni'] . "\n";
    echo "Email: " . $admin['email'] . "\n";
    echo "Cargo: " . $admin['cargo'] . "\n";
    echo "Departamento: " . $admin['departamento'] . "\n";
    echo "Fecha de contratación: " . $admin['fecha_contratacion'] . "\n";
    
    // Verificar contacto de emergencia
    $sqlContacto = "SELECT 
                        ce.nombre_contacto,
                        ce.telefono_principal,
                        ce.telefono_alternativo,
                        ce.email,
                        tce.nombre as tipo_contacto
                    FROM contactos_emergencia ce
                    LEFT JOIN tipos_contacto_emergencia tce ON ce.tipo_contacto_id = tce.id
                    WHERE ce.persona_id = 1";
    
    $stmtContacto = $pdo->prepare($sqlContacto);
    $stmtContacto->execute();
    $contacto = $stmtContacto->fetch();
    
    if ($contacto) {
        echo "\n=== CONTACTO DE EMERGENCIA ===\n";
        echo "Nombre: " . $contacto['nombre_contacto'] . "\n";
        echo "Teléfono principal: " . $contacto['telefono_principal'] . "\n";
        echo "Teléfono alternativo: " . $contacto['telefono_alternativo'] . "\n";
        echo "Email: " . $contacto['email'] . "\n";
    }
    
    echo "\n¡Datos actualizados exitosamente!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
