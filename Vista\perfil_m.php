<?php
require_once '../Controlador/AuthController.php';

// Proteger la página - solo maestros
AuthController::proteger<PERSON><PERSON><PERSON>(['maestro']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información específica del maestro
$infoMaestro = $usuarioActual['informacion_rol'];
$especialidad = $infoMaestro['especialidad'] ?? 'No especificada';
$gradoTutor = $infoMaestro['grado_tutor'] ?? 'Sin grado asignado';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plataforma Educativa - Perfil Maestro</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/perfil.css">
    <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="./img/logo-escuela.svg" alt="Logo Escuela">
                </div>
                <button class="menu-toggle" id="menu-toggle">
                    <span class="material-icons">menu</span>
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar maestro'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                        <?php if ($fotoPerfilUrl): ?>
                            <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                        <?php endif; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                        <p>Maestro(a) - <?php echo htmlspecialchars($especialidad); ?></p>
                        <?php if ($gradoTutor !== 'Sin grado asignado'): ?>
                            <small>Tutor: <?php echo htmlspecialchars($gradoTutor); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                
                <nav class="sidebar-menu">
                    <ul>
                        <li>
                            <a href="inicio_m.php">
                                <span class="material-icons">home</span>
                                <span>Inicio</span>
                            </a>
                        </li>
                        <li class="active">
                            <a href="perfil_m.php">
                                <span class="material-icons">person</span>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li>
                            <a href="cursos_m.html">
                                <span class="material-icons">school</span>
                                <span>Mis Cursos</span>
                            </a>
                        </li>
                        <li>
                            <a href="asistencia_m.html">
                                <span class="material-icons">fact_check</span>
                                <span>Asistencia</span>
                            </a>
                        </li>
                        <li>
                            <a href="mensajes_mp.html">
                                <span class="material-icons">chat</span>
                                <span>Mensajes</span>
                            </a>
                        </li>
                        <li class="separator"></li>
                        <li>
                            <a href="../Controlador/AuthController.php?action=logout">
                                <span class="material-icons">logout</span>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mi Perfil</h1>
                    <p class="current-date">Lunes, 22 de marzo de 2025</p>
                </div>
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <span class="material-icons">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </header>
            
            <div class="content-body">
                <div class="profile-container">
                    <!-- Sección de perfil principal -->
                    <section class="profile-main">
                        <div class="profile-header">
                            <div class="profile-avatar-container">
                                <div class="profile-avatar">
                                    <img src="/placeholder.svg?height=200&width=200" alt="Foto de perfil">
                                </div>
                                <button class="change-avatar-btn">
                                    <span class="material-icons">photo_camera</span>
                                    <span>Cambiar foto</span>
                                </button>
                            </div>
                            <div class="profile-info">
                                <h2>Carlos García Mendoza</h2>
                                <p class="profile-role">Profesor</p>
                                <p class="profile-grade">Matemáticas - Primaria</p>
                                <p class="profile-subject">Especialidad: Matemáticas</p>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de información personal -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">person</span>
                                Información Personal
                            </h3>
                            <button class="edit-section-btn" data-section="personal">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre completo</div>
                                    <div class="info-value">Carlos García Mendoza</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Fecha de nacimiento</div>
                                    <div class="info-value">10/08/1985</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Edad</div>
                                    <div class="info-value">39 años</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Sexo</div>
                                    <div class="info-value">Masculino</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Dirección</div>
                                    <div class="info-value">Av. Educadores 456, Ciudad</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono</div>
                                    <div class="info-value">+51 987 123 456</div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección de cuenta -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">account_circle</span>
                                Información de Cuenta
                            </h3>
                            <button class="edit-section-btn" data-section="account">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre de usuario</div>
                                    <div class="info-value">carlos.garcia</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Correo electrónico</div>
                                    <div class="info-value"><EMAIL></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Contraseña</div>
                                    <div class="info-value password-field">••••••••••
                                        <button class="change-password-btn">
                                            <span class="material-icons">lock</span>
                                            Cambiar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Sección profesional -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">school</span>
                                Información Profesional
                            </h3>
                            <button class="edit-section-btn" data-section="professional">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Especialidad</div>
                                    <div class="info-value">Matemáticas</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Nivel educativo</div>
                                    <div class="info-value">Primaria</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Fecha de contratación</div>
                                    <div class="info-value">15/02/2018</div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    
                    
                    <!-- Sección de contacto de emergencia -->
                    <section class="profile-section">
                        <div class="section-header">
                            <h3>
                                <span class="material-icons">contact_phone</span>
                                Contacto de Emergencia
                            </h3>
                            <button class="edit-section-btn" data-section="emergency">
                                <span class="material-icons">edit</span>
                                Editar
                            </button>
                        </div>
                        
                        <div class="section-content">
                            <div class="profile-info-grid">
                                <div class="info-item">
                                    <div class="info-label">Nombre del contacto</div>
                                    <div class="info-value">Laura Mendoza (Esposa)</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono principal</div>
                                    <div class="info-value">+51 987 654 321</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Teléfono alternativo</div>
                                    <div class="info-value">+51 987 789 012</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Correo electrónico</div>
                                    <div class="info-value"><EMAIL></div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Modal para editar información personal -->
    <div id="edit-personal-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información Personal</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="fullname">Nombre completo</label>
                        <input type="text" id="fullname" value="Carlos García Mendoza">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthdate">Fecha de nacimiento</label>
                            <input type="date" id="birthdate" value="1985-08-10">
                        </div>
                        <div class="form-group">
                            <label for="gender">Sexo</label>
                            <select id="gender">
                                <option value="male" selected>Masculino</option>
                                <option value="female">Femenino</option>
                                <option value="other">Otro</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="address">Dirección</label>
                        <input type="text" id="address" value="Av. Educadores 456, Ciudad">
                    </div>
                    <div class="form-group">
                        <label for="phone">Teléfono</label>
                        <input type="tel" id="phone" value="+51 987 123 456">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para editar información de cuenta -->
    <div id="edit-account-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información de Cuenta</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="username">Nombre de usuario</label>
                        <input type="text" id="username" value="carlos.garcia">
                    </div>
                    <div class="form-group">
                        <label for="email">Correo electrónico</label>
                        <input type="email" id="email" value="<EMAIL>">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para cambiar contraseña -->
    <div id="change-password-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Cambiar Contraseña</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="current-password">Contraseña actual</label>
                        <input type="password" id="current-password">
                    </div>
                    <div class="form-group">
                        <label for="new-password">Nueva contraseña</label>
                        <input type="password" id="new-password">
                    </div>
                    <div class="form-group">
                        <label for="confirm-password">Confirmar nueva contraseña</label>
                        <input type="password" id="confirm-password">
                    </div>
                    
                    <div class="password-requirements">
                        <p>La contraseña debe cumplir con los siguientes requisitos:</p>
                        <ul>
                            <li>Mínimo 8 caracteres</li>
                            <li>Al menos una letra mayúscula</li>
                            <li>Al menos un número</li>
                            <li>Al menos un carácter especial</li>
                        </ul>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Cambiar contraseña</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para editar información profesional -->
    <div id="edit-professional-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Información Profesional</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="specialty">Especialidad</label>
                        <input type="text" id="specialty" value="Matemáticas">
                    </div>
                    <div class="form-group">
                        <label for="education-level">Nivel educativo</label>
                        <select id="education-level">
                            <option value="primary" selected>Primaria</option>
                            <option value="secondary">Secundaria</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="hire-date">Fecha de contratación</label>
                        <input type="date" id="hire-date" value="2018-02-15">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal para editar contacto de emergencia -->
    <div id="edit-emergency-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Editar Contacto de Emergencia</h3>
                <button class="modal-close-btn">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form">
                    <div class="form-group">
                        <label for="emergency-name">Nombre del contacto</label>
                        <input type="text" id="emergency-name" value="Laura Mendoza (Esposa)">
                    </div>
                    <div class="form-group">
                        <label for="emergency-phone">Teléfono principal</label>
                        <input type="tel" id="emergency-phone" value="+51 987 654 321">
                    </div>
                    <div class="form-group">
                        <label for="emergency-alt-phone">Teléfono alternativo</label>
                        <input type="tel" id="emergency-alt-phone" value="+51 987 789 012">
                    </div>
                    <div class="form-group">
                        <label for="emergency-email">Correo electrónico</label>
                        <input type="email" id="emergency-email" value="<EMAIL>">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                        <button type="submit" class="btn-primary">Guardar cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/perfil_m.js"></script>
</body>
</html>