document.addEventListener("DOMContentLoaded", () => {
    // Elementos DOM para videoconferencia
    const meetSection = document.querySelector(".meet-section")
    const createMeetBtn = document.querySelector(".create-meet-btn")
    const meetModal = document.getElementById("meet-modal")
    const meetForm = document.getElementById("meet-form")
    const meetEditBtn = document.querySelector(".meet-action-btn.edit-btn")
    const meetDeleteBtn = document.querySelector(".meet-action-btn.delete-btn")
  
    // Elementos DOM para carpetas y contenido
    const weekHeaders = document.querySelectorAll(".week-header")
    const createFolderBtn = document.querySelector(".create-folder-btn")
    const createContentBtn = document.querySelector(".create-content-btn")
    const folderModal = document.getElementById("folder-modal")
    const contentModal = document.getElementById("content-modal")
    const folderForm = document.getElementById("folder-form")
    const contentForm = document.getElementById("content-form")
    const contentType = document.getElementById("content-type")
    const taskFields = document.getElementById("task-fields")
    const fileFields = document.getElementById("file-fields")
    const linkFields = document.getElementById("link-fields")
    const participationFields = document.getElementById("participation-fields")
    const taskLock = document.getElementById("task-lock")
    const lockDateFields = document.getElementById("lock-date-fields")
    const addContentBtns = document.querySelectorAll(".add-content-btn")
    const weekActionBtns = document.querySelectorAll(".week-action-btn")
    const contentActionBtns = document.querySelectorAll(".content-action-btn")
  
    // Elementos DOM para modales
    const deleteModal = document.getElementById("delete-modal")
    const deleteModalTitle = document.getElementById("delete-modal-title")
    const deleteModalMessage = document.getElementById("delete-modal-message")
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn")
    const modalCloseBtns = document.querySelectorAll(".modal-close-btn")
  
    // Elementos DOM para vista de tarea
    const taskViewModal = document.getElementById("task-view-modal")
    const taskViewCloseBtn = document.getElementById("close-task-view-btn")
  
    // Variables para almacenar elementos a eliminar
    let elementToDelete = null
    let deleteType = ""

    // Variables para rastrear IDs actuales en modales
    let currentTaskId = ""
    let currentParticipationId = ""
    let currentDocumentId = ""
  
    // Estado de la videoconferencia
    let videoconferenceState = {
      exists: true,
      link: "https://zoom.us/j/1234567890?pwd=abc123",
      title: "Clase en vivo: Operaciones con fracciones",
      date: "2025-03-22",
      time: "08:00",
      platform: "zoom",
    }
  
    // Inicializar la página
    init()
  
    function init() {
      // Configurar event listeners
      setupEventListeners()
  
      // Actualizar la sección de videoconferencia
      updateVideoconferenceSection()
    }
  
    function setupEventListeners() {
      // Event listeners para editor de texto enriquecido
      setupRichTextEditor()

      // Event listeners para videoconferencia
      if (createMeetBtn) createMeetBtn.addEventListener("click", openCreateMeetModal)
      if (meetEditBtn) meetEditBtn.addEventListener("click", openEditMeetModal)
      if (meetDeleteBtn) meetDeleteBtn.addEventListener("click", confirmDeleteMeet)
  
      // Event listeners para carpetas y contenido
      weekHeaders.forEach((header) => {
        header.addEventListener("click", toggleWeekContent)
      })
  
      if (createFolderBtn) createFolderBtn.addEventListener("click", openCreateFolderModal)
      if (createContentBtn) createContentBtn.addEventListener("click", openCreateContentModal)

      // Event listeners para tipos de contenido
      if (contentType) contentType.addEventListener("change", toggleContentFields)
      if (taskLock) taskLock.addEventListener("change", toggleLockDateFields)
  
      // Event listeners para botones de agregar contenido
      addContentBtns.forEach((btn) => {
        btn.addEventListener("click", function () {
          const weekId = this.closest(".folder-content").id.split("-")[1]
          openCreateContentModal(weekId)
        })
      })
  
      // Event listeners para botones de acción de semana
      weekActionBtns.forEach((btn) => {
        if (btn.classList.contains("edit-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const weekId = this.dataset.id
            openEditFolderModal(weekId)
          })
        } else if (btn.classList.contains("delete-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const weekId = this.dataset.id
            confirmDeleteFolder(weekId)
          })
        }
      })
  
      // Event listeners para botones de acción de contenido
      contentActionBtns.forEach((btn) => {
        if (btn.classList.contains("edit-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            openEditContentModal(contentId)
          })
        } else if (btn.classList.contains("delete-btn")) {
          btn.addEventListener("click", function (e) {
            e.stopPropagation()
            const contentId = this.dataset.id
            confirmDeleteContent(contentId)
          })

        }
      })
  
      // Event listeners para modales
      modalCloseBtns.forEach((btn) => {
        btn.addEventListener("click", closeAllModals)
      })

      if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", handleDelete)
      }
  
      // Event listeners para formularios
      if (meetForm) meetForm.addEventListener("submit", handleMeetFormSubmit)
      if (folderForm) folderForm.addEventListener("submit", handleFolderFormSubmit)
      if (contentForm) contentForm.addEventListener("submit", handleContentFormSubmit)
  
      // Event listeners para vista de tarea y documentos
      document.querySelectorAll(".content-details").forEach((item) => {
        item.addEventListener("click", function () {
          const contentType = this.closest(".content-item").querySelector(".content-icon")
          if (contentType.classList.contains("task") || contentType.classList.contains("exam")) {
            openTaskView(this.id)
          } else if (contentType.classList.contains("announcement")) {
            // Abrir modal de anuncio
            const announcementModal = document.getElementById("announcement-modal")
            announcementModal.classList.add("active")

            // Agregar event listener para el botón de editar si no existe
            const editAnnouncementBtn = announcementModal.querySelector(".edit-announcement-btn")
            if (editAnnouncementBtn && !editAnnouncementBtn.hasAttribute("data-listener-added")) {
              editAnnouncementBtn.addEventListener("click", function() {
                closeAllModals()
                openEditContentModal(this.closest(".content-details").id)
              })
              editAnnouncementBtn.setAttribute("data-listener-added", "true")
            }
          } else if (contentType.classList.contains("pdf") || contentType.classList.contains("ppt")) {
            // Abrir vista previa de documento
            openDocumentPreview(this.id)
          } else if (contentType.classList.contains("link")) {
            // Abrir enlace en nueva pestaña
            openLinkContent(this.id)
          } else if (contentType.classList.contains("video")) {
            // Abrir video en nueva pestaña
            openVideoContent(this.id)
          } else if (contentType.classList.contains("participation")) {
            // Abrir vista de participación
            openParticipationView(this.id)
          }
        })
      })
  
      if (taskViewCloseBtn) {
        taskViewCloseBtn.addEventListener("click", closeAllModals)
      }
    }
  
    // Funciones para videoconferencia
    function updateVideoconferenceSection() {
      if (videoconferenceState.exists) {
        // Mostrar videoconferencia existente
        meetSection.classList.add("active-meet")
        meetSection.innerHTML = `
                  <div class="meet-info">
                      <div class="meet-icon">
                          <span class="material-icons">videocam</span>
                      </div>
                      <div class="meet-details">
                          <h3>${videoconferenceState.title}</h3>
                          <p>Hoy, ${formatDate(videoconferenceState.date)} - ${formatTime(videoconferenceState.time)}</p>
                          <div class="meet-actions">
                              <a href="${videoconferenceState.link}" class="meet-link" target="_blank">
                                  <span class="material-icons">open_in_new</span>
                                  Unirse a la videoconferencia
                              </a>
                              <button class="meet-action-btn edit-btn">
                                  <span class="material-icons">edit</span>
                              </button>
                              <button class="meet-action-btn delete-btn">
                                  <span class="material-icons">delete</span>
                              </button>
                          </div>
                      </div>
                  </div>
              `
  
        // Actualizar botones de acción
        document.querySelector(".create-meet-btn").style.display = "none"
  
        // Agregar event listeners a los nuevos botones
        document.querySelector(".meet-action-btn.edit-btn").addEventListener("click", openEditMeetModal)
        document.querySelector(".meet-action-btn.delete-btn").addEventListener("click", confirmDeleteMeet)
      } else {
        // Mostrar mensaje de no videoconferencia
        meetSection.classList.remove("active-meet")
        meetSection.innerHTML = `
                  <div class="no-meet-message">
                      <span class="material-icons">videocam_off</span>
                      <p>No hay enlaces de videoconferencia disponibles por ahora</p>
                  </div>
              `
  
        // Mostrar botón de crear
        document.querySelector(".create-meet-btn").style.display = "flex"
        document.querySelector(".create-meet-btn").textContent = "Agregar enlace de videoconferencia"
        document.querySelector(".create-meet-btn").innerHTML =
          '<span class="material-icons">add</span>Agregar enlace de videoconferencia'
      }
    }
  
    function openCreateMeetModal() {
      // Limpiar formulario
      meetForm.reset()
      document.getElementById("meet-id").value = ""
      document.getElementById("meet-modal-title").textContent = "Agregar Enlace de Videoconferencia"
  
      // Establecer fecha y hora actuales
      const now = new Date()
      document.getElementById("meet-date").value = now.toISOString().split("T")[0]
      document.getElementById("meet-time").value =
        now.getHours().toString().padStart(2, "0") + ":" + now.getMinutes().toString().padStart(2, "0")
  
      // Abrir modal
      meetModal.classList.add("active")
    }
  
    function openEditMeetModal() {
      // Llenar formulario con datos existentes (solo campos que existen)
      const meetId = document.getElementById("meet-id")
      const meetTitle = document.getElementById("meet-title")
      const meetDate = document.getElementById("meet-date")
      const meetTime = document.getElementById("meet-time")
      const meetUrl = document.getElementById("meet-url")
      const meetModalTitle = document.getElementById("meet-modal-title")

      if (meetId) meetId.value = "1"
      if (meetTitle) meetTitle.value = videoconferenceState.title
      if (meetDate) meetDate.value = videoconferenceState.date
      if (meetTime) meetTime.value = videoconferenceState.time
      if (meetUrl) meetUrl.value = videoconferenceState.link

      // Cambiar título del modal
      if (meetModalTitle) meetModalTitle.textContent = "Editar Enlace de Videoconferencia"

      // Abrir modal
      if (meetModal) meetModal.classList.add("active")
    }
  
    function confirmDeleteMeet() {
      deleteType = "meet"
      elementToDelete = "1"
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Videoconferencia"
      if (deleteModalMessage) deleteModalMessage.textContent =
        "¿Está seguro que desea eliminar esta videoconferencia? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function handleMeetFormSubmit(e) {
      e.preventDefault()

      // Obtener datos del formulario (solo campos que existen)
      const titleEl = document.getElementById("meet-title")
      const dateEl = document.getElementById("meet-date")
      const timeEl = document.getElementById("meet-time")
      const urlEl = document.getElementById("meet-url")

      const title = titleEl ? titleEl.value : ""
      const date = dateEl ? dateEl.value : ""
      const time = timeEl ? timeEl.value : ""
      const url = urlEl ? urlEl.value : ""

      // Actualizar estado de videoconferencia
      videoconferenceState = {
        exists: true,
        title,
        date,
        time,
        platform: "zoom", // valor por defecto
        link: url,
      }

      // Actualizar UI
      updateVideoconferenceSection()

      // Cerrar modal
      closeAllModals()
    }
  
    // Funciones para carpetas y contenido
    function toggleWeekContent(e) {
      // No ejecutar si se hizo clic en un botón
      if (e.target.classList.contains("week-action-btn") || e.target.closest(".week-action-btn")) {
        return
      }
  
      const weekId = this.dataset.week
      const content = document.getElementById(`week-${weekId}-content`)
      const toggleIcon = this.querySelector(".toggle-icon")
  
      if (content.style.display === "none") {
        content.style.display = "block"
        toggleIcon.textContent = "expand_less"
      } else {
        content.style.display = "none"
        toggleIcon.textContent = "expand_more"
      }
    }
  
    function openCreateFolderModal() {
      // Limpiar formulario
      folderForm.reset()
      document.getElementById("folder-id").value = ""
      document.getElementById("folder-modal-title").textContent = "Crear Nueva Carpeta"
  
      // Abrir modal
      folderModal.classList.add("active")
    }
  
    function openEditFolderModal(weekId) {
      // Llenar formulario con datos existentes
      document.getElementById("folder-id").value = weekId
  
      // Obtener datos de la carpeta
      const weekHeader = document.querySelector(`.week-header[data-week="${weekId}"]`)
      const title = weekHeader.querySelector("h3").textContent
      const dates = weekHeader.querySelector(".week-dates").textContent.split(" - ")
  
      document.getElementById("folder-title").value = title
      document.getElementById("folder-start-date").value = formatDateForInput(dates[0])
      document.getElementById("folder-end-date").value = formatDateForInput(dates[1])
      document.getElementById("folder-description").value = ""
  
      // Cambiar título del modal
      document.getElementById("folder-modal-title").textContent = "Editar Carpeta"
  
      // Abrir modal
      folderModal.classList.add("active")
    }
  
    function confirmDeleteFolder(weekId) {
      deleteType = "folder"
      elementToDelete = weekId
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Carpeta"
      if (deleteModalMessage) deleteModalMessage.textContent =
        "¿Está seguro que desea eliminar esta carpeta y todo su contenido? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function openCreateContentModal(weekId) {
      // Limpiar formulario
      contentForm.reset()
      document.getElementById("content-id").value = ""
      document.getElementById("content-modal-title").textContent = "Crear Nuevo Contenido"
  
      // Ocultar campos específicos
      taskFields.style.display = "none"
      fileFields.style.display = "none"
      linkFields.style.display = "none"
      participationFields.style.display = "none"
  
      // Guardar el ID de la carpeta actual (sin mostrar el selector)
      if (weekId) {
        const hiddenInput = document.createElement("input")
        hiddenInput.type = "hidden"
        hiddenInput.id = "current-folder-id"
        hiddenInput.value = weekId
        contentForm.appendChild(hiddenInput)
      }
  
      // Abrir modal
      contentModal.classList.add("active")
    }
  
    function openEditContentModal(contentId) {
      // Llenar formulario con datos existentes
      document.getElementById("content-id").value = contentId

      // Obtener datos del contenido
      const contentItem = document
        .querySelector(`.content-action-btn.edit-btn[data-id="${contentId}"]`)
        .closest(".content-item")
      const contentIcon = contentItem.querySelector(".content-icon")
      const title = contentItem.querySelector("h4").textContent
      const shortDescription = contentItem.querySelector("p").textContent

      // Obtener la descripción completa usando la función getFullDescription
      const fullDescription = getFullDescription(title, shortDescription)

      // Determinar tipo de contenido
      let type = ""
      if (contentIcon.classList.contains("announcement")) type = "announcement"
      else if (contentIcon.classList.contains("task")) type = "task"
      else if (contentIcon.classList.contains("exam")) type = "exam"
      else if (contentIcon.classList.contains("pdf")) type = "document"
      else if (contentIcon.classList.contains("ppt")) type = "presentation"
      else if (contentIcon.classList.contains("link")) type = "link"
      else if (contentIcon.classList.contains("video")) type = "video"
      else if (contentIcon.classList.contains("participation")) type = "participation"

      // Llenar campos básicos
      document.getElementById("content-type").value = type
      document.getElementById("content-title").value = title

      // Usar el editor de texto enriquecido si está disponible
      const descriptionField = document.getElementById("content-description")
      if (descriptionField) {
        if (descriptionField.contentEditable === "true") {
          // Es un editor de texto enriquecido
          descriptionField.innerHTML = fullDescription.replace(/\n/g, '<br>')
        } else {
          // Es un textarea normal
          descriptionField.value = fullDescription
        }
      }

      // Llenar campos específicos según el tipo
      fillSpecificFields(contentId, type, title, fullDescription)

      // Mostrar campos específicos según el tipo
      toggleContentFields()

      // Cambiar título del modal
      document.getElementById("content-modal-title").textContent = "Editar Contenido"

      // Abrir modal
      contentModal.classList.add("active")
    }

    // Función auxiliar para llenar campos específicos según el tipo de contenido
    function fillSpecificFields(contentId, type, title, description) {
      if (type === "task") {
        // Llenar campos específicos de tarea
        const dueDateField = document.getElementById("task-due-date")
        const pointsField = document.getElementById("task-points")

        if (dueDateField) {
          // Extraer fecha de la vista (formato: "Fecha límite: DD/MM/YYYY")
          const contentItem = document.querySelector(`.content-action-btn.edit-btn[data-id="${contentId}"]`).closest(".content-item")
          const dateText = contentItem.querySelector(".content-date").textContent
          if (dateText.includes("Fecha límite:")) {
            const dateStr = dateText.replace("Fecha límite: ", "")
            dueDateField.value = formatDateForInput(dateStr)
          }
        }

        if (pointsField) {
          pointsField.value = "10" // Valor por defecto
        }
      } else if (type === "link" || type === "video") {
        // Llenar campo de URL
        const urlField = document.getElementById("content-url")
        if (urlField) {
          // Determinar URL basada en el título (en un caso real vendría de la base de datos)
          let url = ""
          if (title.toLowerCase().includes("video") || title.toLowerCase().includes("fracciones")) {
            url = type === "video" ? "https://www.youtube.com/watch?v=3MRlBKgVqp8" : "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
          } else {
            url = "https://www.google.com"
          }
          urlField.value = url
        }
      } else if (type === "participation") {
        // Llenar campos específicos de participación
        const maxGradeField = document.getElementById("participation-max-grade")
        if (maxGradeField) {
          maxGradeField.value = "20" // Valor por defecto
        }
      }
    }
  
    function confirmDeleteContent(contentId) {
      deleteType = "content"
      elementToDelete = contentId
      if (deleteModalTitle) deleteModalTitle.textContent = "Eliminar Contenido"
      if (deleteModalMessage) deleteModalMessage.textContent = "¿Está seguro que desea eliminar este contenido? Esta acción no se puede deshacer."
      if (deleteModal) deleteModal.classList.add("active")
    }
  
    function handleFolderFormSubmit(e) {
      e.preventDefault()
  
      // Obtener datos del formulario
      const id = document.getElementById("folder-id").value
      const title = document.getElementById("folder-title").value
      const startDate = document.getElementById("folder-start-date").value
      const endDate = document.getElementById("folder-end-date").value
  
      // Formatear fechas
      const formattedStartDate = formatDate(startDate)
      const formattedEndDate = formatDate(endDate)
  
      if (id) {
        // Editar carpeta existente
        const weekHeader = document.querySelector(`.week-header[data-week="${id}"]`)
        weekHeader.querySelector("h3").textContent = title
        weekHeader.querySelector(".week-dates").textContent = `${formattedStartDate} - ${formattedEndDate}`
      } else {
        // Crear nueva carpeta (simulado)
        alert("Carpeta creada correctamente: " + title)
      }
  
      // Cerrar modal
      closeAllModals()
    }
  
    function handleContentFormSubmit(e) {
      e.preventDefault()
  
      // Obtener datos del formulario
      const id = document.getElementById("content-id").value
      const type = document.getElementById("content-type").value
      const title = document.getElementById("content-title").value
      const description = document.getElementById("content-description").value
  
      if (id) {
        // Editar contenido existente
        const contentItem = document
          .querySelector(`.content-action-btn.edit-btn[data-id="${id}"]`)
          .closest(".content-item")
        contentItem.querySelector("h4").textContent = title
        contentItem.querySelector("p").textContent = description
      } else {
        // Crear nuevo contenido (simulado)
        alert("Contenido creado correctamente: " + title)
      }
  
      // Cerrar modal
      closeAllModals()
    }
  
    function toggleContentFields() {
      const type = contentType.value

      // Ocultar todos los campos específicos
      taskFields.style.display = "none"
      fileFields.style.display = "none"
      linkFields.style.display = "none"
      participationFields.style.display = "none"

      // Mostrar campos según el tipo seleccionado
      if (type === "task" || type === "exam") {
        taskFields.style.display = "block"
      } else if (type === "document" || type === "presentation") {
        fileFields.style.display = "block"
      } else if (type === "link" || type === "video") {
        linkFields.style.display = "block"
      } else if (type === "participation") {
        participationFields.style.display = "block"
      }
    }
  
    function toggleLockDateFields() {
      if (taskLock.checked) {
        lockDateFields.style.display = "block"
      } else {
        lockDateFields.style.display = "none"
      }
    }
  
    // Funciones para eliminar elementos
    function handleDelete() {
      if (deleteType === "meet") {
        // Eliminar videoconferencia
        videoconferenceState.exists = false
        updateVideoconferenceSection()
      } else if (deleteType === "folder") {
        // Simular eliminación de carpeta
        alert("Carpeta eliminada correctamente")
      } else if (deleteType === "content") {
        // Simular eliminación de contenido
        alert("Contenido eliminado correctamente")
      }
  
      // Cerrar modal
      closeAllModals()
    }
  
    // Funciones para vista de tarea
    function openTaskView(taskId) {
      // Guardar ID actual
      currentTaskId = taskId

      // Obtener datos del contenido
      const contentItem = document.getElementById(taskId).closest(".content-item")
      const contentIcon = contentItem.querySelector(".content-icon")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Si no existe el modal de vista de tarea, crearlo
      if (!document.getElementById("task-view-modal")) {
        createTaskViewModal(title, description, date)
      } else {
        // Actualizar contenido del modal existente
        updateTaskViewModal(title, description, date)
      }

      // Abrir modal
      document.getElementById("task-view-modal").classList.add("active")
    }
  
    // Crear modal de vista de tarea con datos dinámicos
    function createTaskViewModal(title, description, date) {
      const modal = document.createElement("div")
      modal.id = "task-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content assignment-container">
              <div class="assignment-header">
                  <h1 class="assignment-title">
                      <span class="material-icons">assignment</span>
                      ${title}
                  </h1>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>

              <div class="assignment-content">
                  <div class="assignment-section">
                      <h2 class="assignment-section-title">Descripción de la tarea</h2>
                      <p class="assignment-description">${getFullDescription(title, description)}</p>
                      <img src="unnie.png" alt="Ejercicios de sumas y restas" class="assignment-image">
                  </div>

                  <div class="assignment-section">
                      <div class="assignment-meta">
                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">event</span>
                                  Fecha de entrega
                              </div>
                              <div class="meta-value">${date.replace('Fecha límite: ', '')}, 12:00 AM</div>
                          </div>

                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">grade</span>
                                  Puntos
                              </div>
                              <div class="meta-value">10 puntos máximos</div>
                          </div>

                          <div class="meta-item">
                              <div class="meta-label">
                                  <span class="material-icons">person</span>
                                  Asignado por
                              </div>
                              <div class="meta-value">Prof. Carlos García</div>
                          </div>
                      </div>
                  </div>

                  <div class="assignment-section">
                      <div class="submission-section">
                          <h2 class="submission-title">Estado de la tarea</h2>
                          <div class="task-status-info">
                              <div class="status-item">
                                  <span class="material-icons status-icon">schedule</span>
                                  <div class="status-content">
                                      <span class="status-label">Estado actual</span>
                                      <span class="status-value">Activa - Los estudiantes pueden entregar hasta el ${date.replace('Fecha límite: ', '')}</span>
                                  </div>
                              </div>
                          </div>
                          <div class="submission-options">
                              <div class="form-actions">
                                  <button type="button" class="btn-secondary" id="close-task-view-btn">Cerrar</button>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      document.getElementById("close-task-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de tarea con nuevos datos
    function updateTaskViewModal(title, description, date) {
      const modal = document.getElementById("task-view-modal")
      const titleElement = modal.querySelector(".assignment-title")
      const descriptionElement = modal.querySelector(".assignment-description")
      const dateElement = modal.querySelector(".meta-value")
      const statusElement = modal.querySelector(".status-value")

      titleElement.innerHTML = `<span class="material-icons">assignment</span>${title}`
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.textContent = `${date.replace('Fecha límite: ', '')}, 12:00 AM`
      if (statusElement) {
        statusElement.textContent = `Activa - Los estudiantes pueden entregar hasta el ${date.replace('Fecha límite: ', '')}`
      }
    }

    // Función para abrir vista previa de documentos
    function openDocumentPreview(documentId) {
      // Guardar ID actual
      currentDocumentId = documentId

      // Obtener datos del contenido
      const contentItem = document.getElementById(documentId).closest(".content-item")
      const contentIcon = contentItem.querySelector(".content-icon")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Actualizar contenido del modal
      const documentModal = document.getElementById("document-preview-modal")
      const documentTitle = document.getElementById("document-title")
      const documentDescription = documentModal.querySelector(".document-description")
      const documentDate = documentModal.querySelector(".document-date")
      const documentType = documentModal.querySelector(".document-type")
      const documentIcon = documentModal.querySelector(".document-placeholder .material-icons")
      const documentFilename = documentModal.querySelector(".document-filename")

      if (documentTitle) documentTitle.textContent = title
      if (documentDescription) documentDescription.textContent = getFullDescription(title, description)
      if (documentDate) documentDate.textContent = date

      // Determinar tipo de documento, icono y nombre de archivo
      if (contentIcon.classList.contains("pdf")) {
        if (documentType) documentType.textContent = "Documento PDF"
        if (documentIcon) documentIcon.textContent = "picture_as_pdf"
        if (documentFilename) documentFilename.textContent = "guia_ejercicios_fracciones.pdf"
      } else if (contentIcon.classList.contains("ppt")) {
        if (documentType) documentType.textContent = "Presentación PowerPoint"
        if (documentIcon) documentIcon.textContent = "slideshow"
        if (documentFilename) documentFilename.textContent = "presentacion_fracciones.pptx"
      }

      if (documentModal) {
        documentModal.classList.add("active")

        // Agregar event listeners para los botones si no existen
        const editBtn = document.getElementById("edit-document-btn")
        const downloadBtn = document.getElementById("download-document-btn")

        if (editBtn && !editBtn.hasAttribute("data-listener-added")) {
          editBtn.addEventListener("click", function() {
            closeAllModals()
            openEditContentModal(currentDocumentId)
          })
          editBtn.setAttribute("data-listener-added", "true")
        }

        if (downloadBtn && !downloadBtn.hasAttribute("data-listener-added")) {
          downloadBtn.addEventListener("click", function() {
            // Simular descarga
            alert("Descargando documento...")
          })
          downloadBtn.setAttribute("data-listener-added", "true")
        }
      }
    }

    // Función para abrir contenido de tipo enlace
    function openLinkContent(linkId) {
      // Obtener datos del contenido
      const contentItem = document.getElementById(linkId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Determinar URL basada en el título (en un caso real vendría de la base de datos)
      let url = ""
      if (title.toLowerCase().includes("video") || title.toLowerCase().includes("fracciones")) {
        url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // URL de ejemplo
      } else {
        url = "https://www.google.com" // URL por defecto
      }

      // Si no existe el modal de vista de enlace, crearlo
      if (!document.getElementById("link-view-modal")) {
        createLinkViewModal(title, description, date, url)
      } else {
        // Actualizar contenido del modal existente
        updateLinkViewModal(title, description, date, url)
      }

      // Abrir modal
      document.getElementById("link-view-modal").classList.add("active")
    }

    // Función para abrir contenido de tipo video
    function openVideoContent(videoId) {
      // Obtener datos del contenido
      const contentItem = document.getElementById(videoId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Determinar URL del video basada en el título (en un caso real vendría de la base de datos)
      let url = ""
      if (title.toLowerCase().includes("tutorial") || title.toLowerCase().includes("fracciones")) {
        url = "https://www.youtube.com/watch?v=3MRlBKgVqp8" // URL de ejemplo para tutorial de fracciones
      } else {
        url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // URL por defecto
      }

      // Si no existe el modal de vista de video, crearlo
      if (!document.getElementById("video-view-modal")) {
        createVideoViewModal(title, description, date, url)
      } else {
        // Actualizar contenido del modal existente
        updateVideoViewModal(title, description, date, url)
      }

      // Abrir modal
      document.getElementById("video-view-modal").classList.add("active")
    }

    // Función para abrir vista de participación
    function openParticipationView(participationId) {
      // Guardar ID actual
      currentParticipationId = participationId

      // Obtener datos del contenido
      const contentItem = document.getElementById(participationId).closest(".content-item")
      const title = contentItem.querySelector("h4").textContent
      const description = contentItem.querySelector("p").textContent
      const date = contentItem.querySelector(".content-date").textContent

      // Si no existe el modal de vista de participación, crearlo
      if (!document.getElementById("participation-view-modal")) {
        createParticipationViewModal(title, description, date)
      } else {
        // Actualizar contenido del modal existente
        updateParticipationViewModal(title, description, date)
      }

      // Abrir modal
      document.getElementById("participation-view-modal").classList.add("active")
    }

    // Crear modal de vista de participación con datos dinámicos
    function createParticipationViewModal(title, description, date) {
      const modal = document.createElement("div")
      modal.id = "participation-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content modal-large">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="participation-container">
                      <div class="participation-info">
                          <p><strong>Descripción:</strong></p>
                          <p>${getFullDescription(title, description)}</p>
                          <p><strong>${date}</strong></p>
                      </div>

                      <div class="participation-grades">
                          <h3>Calificaciones de Participación</h3>
                          <div class="students-list">
                              <div class="student-item">
                                  <span class="student-name">Ana García López</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="18" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">Carlos Mendoza Silva</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="16" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">María Rodríguez Torres</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="19" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">José Fernández Cruz</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="15" readonly>
                              </div>
                              <div class="student-item">
                                  <span class="student-name">Lucía Vargas Morales</span>
                                  <input type="number" class="participation-grade" min="0" max="20" step="0.5" value="17" readonly>
                              </div>
                          </div>

                          <div class="participation-actions">
                              <button type="button" class="btn-secondary" id="close-participation-view-btn">Cerrar</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listener al botón de cerrar
      document.getElementById("close-participation-view-btn").addEventListener("click", closeAllModals)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      document.getElementById("close-participation-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de participación con nuevos datos
    function updateParticipationViewModal(title, description, date) {
      const modal = document.getElementById("participation-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".participation-info p:nth-child(2)")
      const dateElement = modal.querySelector(".participation-info p:last-child")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
    }


  
    // Crear modal de vista de enlace con datos dinámicos
    function createLinkViewModal(title, description, date, url) {
      const modal = document.createElement("div")
      modal.id = "link-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="content-details-view">
                      <div class="content-icon-large">
                          <span class="material-icons">link</span>
                      </div>
                      <div class="content-info">
                          <p class="content-description">${description}</p>
                          <p class="content-date"><strong>${date}</strong></p>
                          <div class="content-url">
                              <p><strong>Enlace:</strong> <a href="${url}" target="_blank">${url}</a></p>
                          </div>
                      </div>
                  </div>
                  <div class="form-actions">
                      <button type="button" class="btn-secondary close-link-view-btn">Cerrar</button>
                      <button type="button" class="btn-primary" onclick="window.open('${url}', '_blank')">
                          <span class="material-icons">open_in_new</span>
                          Abrir enlace
                      </button>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      modal.querySelector(".close-link-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de enlace con nuevos datos
    function updateLinkViewModal(title, description, date, url) {
      const modal = document.getElementById("link-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".content-description")
      const dateElement = modal.querySelector(".content-date")
      const urlElement = modal.querySelector(".content-url a")
      const openButton = modal.querySelector(".btn-primary")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
      urlElement.textContent = url
      urlElement.href = url
      openButton.onclick = () => window.open(url, '_blank')
    }

    // Crear modal de vista de video con datos dinámicos
    function createVideoViewModal(title, description, date, url) {
      const modal = document.createElement("div")
      modal.id = "video-view-modal"
      modal.className = "modal-overlay"

      modal.innerHTML = `
          <div class="modal-content">
              <div class="modal-header">
                  <h3>${title}</h3>
                  <button class="modal-close-btn">
                      <span class="material-icons">close</span>
                  </button>
              </div>
              <div class="modal-body">
                  <div class="content-details-view">
                      <div class="content-icon-large">
                          <span class="material-icons">play_circle</span>
                      </div>
                      <div class="content-info">
                          <p class="content-description">${description}</p>
                          <p class="content-date"><strong>${date}</strong></p>
                          <div class="video-preview">
                              <div class="video-thumbnail">
                                  <span class="material-icons play-icon">play_circle_filled</span>
                                  <p>Haz clic para reproducir el video</p>
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="form-actions">
                      <button type="button" class="btn-secondary close-video-view-btn">Cerrar</button>
                      <button type="button" class="btn-primary" onclick="window.open('${url}', '_blank')">
                          <span class="material-icons">play_arrow</span>
                          Reproducir video
                      </button>
                  </div>
              </div>
          </div>
      `

      document.body.appendChild(modal)

      // Agregar event listeners
      modal.querySelector(".modal-close-btn").addEventListener("click", closeAllModals)
      modal.querySelector(".close-video-view-btn").addEventListener("click", closeAllModals)
    }

    // Actualizar modal de vista de video con nuevos datos
    function updateVideoViewModal(title, description, date, url) {
      const modal = document.getElementById("video-view-modal")
      const titleElement = modal.querySelector(".modal-header h3")
      const descriptionElement = modal.querySelector(".content-description")
      const dateElement = modal.querySelector(".content-date")
      const openButton = modal.querySelector(".btn-primary")

      titleElement.textContent = title
      descriptionElement.textContent = getFullDescription(title, description)
      dateElement.innerHTML = `<strong>${date}</strong>`
      openButton.onclick = () => window.open(url, '_blank')
    }

    // Función para obtener descripción completa basada en el título
    function getFullDescription(title, shortDescription) {
      // Mapeo de descripciones completas basadas en el título
      const fullDescriptions = {
        "Tarea: Ejercicios de Fracciones": "Realiza las sumas y restas de la pág. 19 - 20 del libro de texto. Incluye ejercicios de aplicación práctica donde debes resolver problemas de la vida cotidiana usando fracciones. Muestra todos los pasos de tu trabajo y simplifica las respuestas cuando sea posible. Tiempo estimado: 45 minutos.",
        "Tarea: Multiplicación de fracciones": "Resuelve los ejercicios de multiplicación de fracciones de las páginas 25-27 del libro de texto. Incluye problemas de aplicación práctica donde debes multiplicar fracciones para resolver situaciones de la vida real como recetas de cocina, medidas de construcción y distribución de recursos. Muestra todos los pasos de tu trabajo y simplifica las respuestas cuando sea posible.",
        "Presentación: Introducción a las fracciones": "Presentación interactiva de 25 diapositivas que cubre conceptos básicos de fracciones, representación gráfica con ejemplos visuales, ejercicios prácticos y actividades de comprensión. Incluye animaciones y elementos multimedia para facilitar el aprendizaje.",
        "Documento: Guía de ejercicios": "Guía completa de 15 páginas con ejercicios prácticos sobre fracciones organizados por nivel de dificultad. Incluye problemas de suma, resta, multiplicación y división de fracciones, con ejemplos resueltos paso a paso y espacios para que los estudiantes practiquen. Formato PDF descargable e imprimible.",
        "Enlace: Video explicativo sobre fracciones": "Video educativo de 12 minutos que explica de manera visual y didáctica los conceptos fundamentales de las fracciones. Incluye ejemplos con objetos cotidianos, representaciones gráficas coloridas y ejercicios interactivos. Ideal como material de apoyo para reforzar lo aprendido en clase y para estudiantes que necesiten repasar los conceptos básicos.",
        "Video: Tutorial interactivo de fracciones": "Video tutorial de 18 minutos con ejercicios interactivos y animaciones 3D que enseña los conceptos básicos de fracciones de manera dinámica. Incluye subtítulos, controles de velocidad de reproducción y marcadores de capítulos para facilitar la navegación. Los estudiantes pueden pausar en cualquier momento para practicar con los ejercicios integrados.",
        "Participación: Discusión sobre fracciones en la vida cotidiana": "Actividad de participación oral donde los estudiantes comparten ejemplos de fracciones que encuentran en su vida diaria. Se evalúa la participación activa, la creatividad de los ejemplos y la comprensión de los conceptos. Los estudiantes pueden ganar puntos adicionales por ayudar a sus compañeros y hacer preguntas reflexivas.",
        "Anuncio: Bienvenidos al curso de matemáticas": "¡Bienvenidos al curso de matemáticas de 5° grado! En este curso aprenderemos sobre fracciones, decimales, geometría y muchos temas más. Es importante que revisen el material de clase regularmente y completen las tareas asignadas. Objetivos del curso: Comprender y aplicar operaciones con fracciones, resolver problemas matemáticos de la vida cotidiana, desarrollar habilidades de razonamiento lógico y aprender conceptos básicos de geometría."
      }

      return fullDescriptions[title] || shortDescription
    }

    // Funciones auxiliares
    function closeAllModals() {
      document.querySelectorAll(".modal-overlay").forEach((modal) => {
        modal.classList.remove("active")
      })
    }
  
    // Funciones auxiliares para obtener IDs actuales
    function getCurrentTaskId() {
      return currentTaskId
    }

    function getCurrentParticipationId() {
      return currentParticipationId
    }

    function getCurrentDocumentId() {
      return currentDocumentId
    }

    function formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString("es-ES", { day: "2-digit", month: "2-digit", year: "numeric" })
    }
  
    function formatDateForInput(dateString) {
      // Convertir formato DD/MM/YYYY a YYYY-MM-DD
      const parts = dateString.split("/")
      return `${parts[2]}-${parts[1]}-${parts[0]}`
    }
  
    function formatTime(timeString) {
      // Convertir formato 24h a 12h
      const [hours, minutes] = timeString.split(":")
      const hour = Number.parseInt(hours)
      const ampm = hour >= 12 ? "PM" : "AM"
      const hour12 = hour % 12 || 12
      return `${hour12}:${minutes} ${ampm}`
    }

    // Configurar editor de texto enriquecido
    function setupRichTextEditor() {
      const toolbarBtns = document.querySelectorAll('.toolbar-btn')
      const editorContent = document.getElementById('content-description')
      const imageUpload = document.getElementById('image-upload')
      const insertImageBtn = document.getElementById('insert-image-btn')

      // Event listeners para botones de la barra de herramientas
      toolbarBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.preventDefault()
          const command = btn.getAttribute('data-command')

          if (command === 'createLink') {
            const url = prompt('Ingresa la URL del enlace:')
            if (url) {
              document.execCommand(command, false, url)
            }
          } else if (command) {
            document.execCommand(command, false, null)
          }

          // Actualizar estado de botones
          updateToolbarState()
        })
      })

      // Event listener para insertar imagen
      if (insertImageBtn) {
        insertImageBtn.addEventListener('click', (e) => {
          e.preventDefault()
          imageUpload.click()
        })
      }

      // Event listener para subir imagen
      if (imageUpload) {
        imageUpload.addEventListener('change', (e) => {
          const file = e.target.files[0]
          if (file) {
            const reader = new FileReader()
            reader.onload = (e) => {
              const img = `<img src="${e.target.result}" alt="Imagen insertada" style="max-width: 100%; height: auto;">`
              document.execCommand('insertHTML', false, img)
            }
            reader.readAsDataURL(file)
          }
        })
      }

      // Event listener para actualizar estado de botones al cambiar selección
      if (editorContent) {
        editorContent.addEventListener('mouseup', updateToolbarState)
        editorContent.addEventListener('keyup', updateToolbarState)
      }
    }

    function updateToolbarState() {
      const toolbarBtns = document.querySelectorAll('.toolbar-btn')

      toolbarBtns.forEach(btn => {
        const command = btn.getAttribute('data-command')
        if (command && document.queryCommandState(command)) {
          btn.classList.add('active')
        } else {
          btn.classList.remove('active')
        }
      })
    }
  })

  // Función global para descargar archivos
  function downloadFile(filename) {
    console.log("Descargando archivo:", filename)

    // Crear un enlace temporal para la descarga
    const link = document.createElement('a')
    link.href = `./archivos/${filename}`
    link.download = filename
    link.style.display = 'none'

    // Agregar al DOM, hacer clic y remover
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Mostrar mensaje de confirmación
    showDownloadMessage(filename)
  }

  // Función para mostrar mensajes de descarga
  function showDownloadMessage(filename) {
    // Crear elemento de notificación
    const notification = document.createElement('div')
    notification.className = 'download-notification'
    notification.innerHTML = `
      <div class="notification-content">
        <span class="material-icons">download_done</span>
        <span>Descargando: ${filename}</span>
      </div>
    `

    // Agregar estilos
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      animation: slideIn 0.3s ease-out;
    `

    // Agregar animación CSS si no existe
    if (!document.querySelector('#download-animation-styles')) {
      const animationStyles = document.createElement('style')
      animationStyles.id = 'download-animation-styles'
      animationStyles.textContent = `
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes slideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
      `
      document.head.appendChild(animationStyles)
    }

    // Mostrar notificación
    document.body.appendChild(notification)

    // Remover después de 3 segundos
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-out'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }
