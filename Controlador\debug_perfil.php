<?php
session_start();
require_once '../Modelo/Conexion.php';
require_once 'AuthController.php';

// Verificar que el usuario esté autenticado
if (!AuthController::estaAutenticado()) {
    echo json_encode(['error' => 'No autenticado']);
    exit();
}

$usuarioActual = AuthController::obtenerUsuarioActual();

echo "<h2>Debug - Información del Usuario</h2>";
echo "<h3>Datos de sesión:</h3>";
echo "<pre>";
print_r($usuarioActual);
echo "</pre>";

// Verificar datos en la base de datos
try {
    $pdo = Conexion::getConexion();
    
    echo "<h3>Datos en la base de datos:</h3>";
    
    // Consultar usuario
    $sqlUsuario = "SELECT * FROM usuarios WHERE id = :id";
    $stmtUsuario = $pdo->prepare($sqlUsuario);
    $stmtUsuario->bindParam(':id', $usuarioActual['id']);
    $stmtUsuario->execute();
    $usuario = $stmtUsuario->fetch();
    
    echo "<h4>Tabla usuarios:</h4>";
    echo "<pre>";
    print_r($usuario);
    echo "</pre>";
    
    // Consultar persona
    $sqlPersona = "SELECT * FROM personas WHERE usuario_id = :usuario_id";
    $stmtPersona = $pdo->prepare($sqlPersona);
    $stmtPersona->bindParam(':usuario_id', $usuarioActual['id']);
    $stmtPersona->execute();
    $persona = $stmtPersona->fetch();
    
    echo "<h4>Tabla personas:</h4>";
    echo "<pre>";
    print_r($persona);
    echo "</pre>";
    
    // Consultar administrador
    if ($persona) {
        $sqlAdmin = "SELECT * FROM administradores WHERE persona_id = :persona_id";
        $stmtAdmin = $pdo->prepare($sqlAdmin);
        $stmtAdmin->bindParam(':persona_id', $persona['id']);
        $stmtAdmin->execute();
        $admin = $stmtAdmin->fetch();
        
        echo "<h4>Tabla administradores:</h4>";
        echo "<pre>";
        print_r($admin);
        echo "</pre>";
    }
    
    // Consultar contacto de emergencia
    if ($persona) {
        $sqlContacto = "SELECT * FROM contactos_emergencia WHERE persona_id = :persona_id";
        $stmtContacto = $pdo->prepare($sqlContacto);
        $stmtContacto->bindParam(':persona_id', $persona['id']);
        $stmtContacto->execute();
        $contacto = $stmtContacto->fetch();
        
        echo "<h4>Tabla contactos_emergencia:</h4>";
        echo "<pre>";
        print_r($contacto);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}

// Mostrar datos POST si existen
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Datos POST recibidos:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
}

echo "<hr>";
echo "<p><a href='../Vista/perfil_a.php'>Volver al perfil</a></p>";
?>
