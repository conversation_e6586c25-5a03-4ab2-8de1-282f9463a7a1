<?php
require_once '../Controlador/AuthController.php';
require_once '../Modelo/Administrador.php';
require_once '../Modelo/Conexion.php';

// Proteger la página - solo administradores
AuthController::proteger<PERSON><PERSON>na(['administrador']);

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información completa del administrador desde la base de datos
$administrador = new Administrador();
$datosCompletos = $administrador->obtenerPorUsuarioId($usuarioActual['id']);

// Obtener contacto de emergencia si existe
$pdo = Conexion::getConexion();
$sqlContacto = "SELECT ce.*, tce.nombre as tipo_contacto_nombre
                FROM contactos_emergencia ce
                LEFT JOIN tipos_contacto_emergencia tce ON ce.tipo_contacto_id = tce.id
                WHERE ce.persona_id = :persona_id
                LIMIT 1";
$stmtContacto = $pdo->prepare($sqlContacto);
$stmtContacto->bindParam(':persona_id', $datosCompletos['persona_id']);
$stmtContacto->execute();
$contactoEmergencia = $stmtContacto->fetch();

// Calcular edad si existe fecha de nacimiento
$edad = null;
if (!empty($datosCompletos['fecha_nacimiento'])) {
    $fechaNacimiento = new DateTime($datosCompletos['fecha_nacimiento']);
    $hoy = new DateTime();
    $edad = $hoy->diff($fechaNacimiento)->y;
}

// Formatear fechas para mostrar
$fechaNacimientoFormateada = null;
$fechaContratacionFormateada = null;

if (!empty($datosCompletos['fecha_nacimiento'])) {
    $fechaNacimientoFormateada = date('d/m/Y', strtotime($datosCompletos['fecha_nacimiento']));
}

if (!empty($datosCompletos['fecha_contratacion'])) {
    $fechaContratacionFormateada = date('d/m/Y', strtotime($datosCompletos['fecha_contratacion']));
}

// Formatear sexo para mostrar
$sexoFormateado = '';
if (!empty($datosCompletos['sexo'])) {
    $sexoFormateado = ($datosCompletos['sexo'] === 'masculino') ? 'Masculino' : 'Femenino';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Perfil Administrador</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/admin.css">
  <link rel="stylesheet" href="./Css/perfil_a.css">
  <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
  <div class="plataforma-container">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                      <?php if ($fotoPerfilUrl): ?>
                          <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                      <p>Administrador</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_a.php">
                              <span class="material-icons">dashboard</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="perfil_a.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li>
                          <a href="usuarios_a.html">
                              <span class="material-icons">people</span>
                              <span>Usuarios</span>
                          </a>
                      </li>
                      <li>
                          <a href="crear_anuncio.html">
                              <span class="material-icons">campaign</span>
                              <span>Anuncios</span>
                          </a>
                      </li>
                      <li>
                          <a href="admision_a.html">
                              <span class="material-icons">how_to_reg</span>
                              <span>Solicitudes de Admisión</span>
                          </a>
                      </li>
                      <li>
                          <a href="configuracion_a.html">
                              <span class="material-icons">settings</span>
                              <span>Configuración</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="../Controlador/AuthController.php?action=logout">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <header class="content-header">
              <div class="header-left">
                  <h1>Mi Perfil</h1>
                  <p class="current-date">Lunes, 22 de marzo de 2025</p>
              </div>
              <div class="header-right">
                  <div class="notifications">
                      <button class="notification-btn">
                          <span class="material-icons">notifications</span>
                          <span class="notification-badge">5</span>
                      </button>
                  </div>
              </div>
          </header>
          
          <div class="content-body">
              <div class="profile-container">
                  <!-- Sección de perfil principal -->
                  <section class="profile-main">
                      <div class="profile-header">
                          <div class="profile-avatar-container">
                              <div class="profile-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                                  <?php if ($fotoPerfilUrl): ?>
                                      <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                                  <?php endif; ?>
                              </div>
                              <button class="change-avatar-btn">
                                  <span class="material-icons">photo_camera</span>
                                  <span>Cambiar foto</span>
                              </button>
                          </div>
                          <div class="profile-info">
                              <h2><?php echo htmlspecialchars($nombreCompleto); ?></h2>
                              <p class="profile-role"><?php echo htmlspecialchars($datosCompletos['cargo'] ?? 'Administrador del Sistema'); ?></p>
                              <p class="profile-grade"><?php echo htmlspecialchars($datosCompletos['departamento'] ?? 'Acceso Total'); ?></p>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de información personal -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">person</span>
                              Información Personal
                          </h3>
                          <button class="edit-section-btn" data-section="personal">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre completo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($nombreCompleto); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">DNI</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['dni'] ?? 'No registrado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Fecha de nacimiento</div>
                                  <div class="info-value"><?php echo htmlspecialchars($fechaNacimientoFormateada ?? 'No registrada'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Edad</div>
                                  <div class="info-value"><?php echo $edad ? $edad . ' años' : 'No calculada'; ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Sexo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($sexoFormateado ?: 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Dirección</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['direccion'] ?? 'No registrada'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['telefono'] ?? 'No registrado'); ?></div>
                              </div>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de cuenta -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">account_circle</span>
                              Información de Cuenta
                          </h3>
                          <button class="edit-section-btn" data-section="account">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre de usuario</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Correo electrónico</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['email']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Contraseña</div>
                                  <div class="info-value password-field">••••••••••
                                      <button class="change-password-btn">
                                          <span class="material-icons">lock</span>
                                          Cambiar
                                      </button>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección profesional -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">work</span>
                              Información Profesional
                          </h3>
                          <button class="edit-section-btn" data-section="professional">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Cargo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['cargo'] ?? 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Departamento</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['departamento'] ?? 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Fecha de contratación</div>
                                  <div class="info-value"><?php echo htmlspecialchars($fechaContratacionFormateada ?? 'No registrada'); ?></div>
                              </div>
                          </div>
                      </div>
                  </section>

                  
                  <!-- Sección de contacto de emergencia -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">contact_phone</span>
                              Contacto de Emergencia
                          </h3>
                          <button class="edit-section-btn" data-section="emergency">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <?php if ($contactoEmergencia): ?>
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre del contacto</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['nombre_contacto']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono principal</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['telefono_principal']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono alternativo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['telefono_alternativo'] ?? 'No registrado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Correo electrónico</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['email'] ?? 'No registrado'); ?></div>
                              </div>
                          </div>
                          <?php else: ?>
                          <div class="no-data-message">
                              <p>No hay contacto de emergencia registrado.</p>
                              <button class="btn-primary add-emergency-contact-btn">
                                  <span class="material-icons">add</span>
                                  Agregar contacto de emergencia
                              </button>
                          </div>
                          <?php endif; ?>
                      </div>
                  </section>
              </div>
          </div>
      </main>
  </div>
  
  <!-- Modal para editar información personal -->
  <div id="edit-personal-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información Personal</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form">
                  <div class="form-row">
                      <div class="form-group">
                          <label for="nombres">Nombres</label>
                          <input type="text" id="nombres" name="nombres" value="<?php echo htmlspecialchars($datosCompletos['nombres'] ?? ''); ?>">
                      </div>
                      <div class="form-group">
                          <label for="apellido_paterno">Apellido paterno</label>
                          <input type="text" id="apellido_paterno" name="apellido_paterno" value="<?php echo htmlspecialchars($datosCompletos['apellido_paterno'] ?? ''); ?>">
                      </div>
                  </div>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="apellido_materno">Apellido materno</label>
                          <input type="text" id="apellido_materno" name="apellido_materno" value="<?php echo htmlspecialchars($datosCompletos['apellido_materno'] ?? ''); ?>">
                      </div>
                      <div class="form-group">
                          <label for="dni">DNI</label>
                          <input type="text" id="dni" name="dni" value="<?php echo htmlspecialchars($datosCompletos['dni'] ?? ''); ?>" maxlength="8">
                      </div>
                  </div>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="birthdate">Fecha de nacimiento</label>
                          <input type="date" id="birthdate" name="fecha_nacimiento" value="<?php echo htmlspecialchars($datosCompletos['fecha_nacimiento'] ?? ''); ?>">
                      </div>
                      <div class="form-group">
                          <label for="gender">Sexo</label>
                          <select id="gender" name="sexo">
                              <option value="masculino" <?php echo ($datosCompletos['sexo'] ?? '') === 'masculino' ? 'selected' : ''; ?>>Masculino</option>
                              <option value="femenino" <?php echo ($datosCompletos['sexo'] ?? '') === 'femenino' ? 'selected' : ''; ?>>Femenino</option>
                          </select>
                      </div>
                  </div>
                  <div class="form-group">
                      <label for="address">Dirección</label>
                      <input type="text" id="address" name="direccion" value="<?php echo htmlspecialchars($datosCompletos['direccion'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="phone">Teléfono</label>
                      <input type="tel" id="phone" name="telefono" value="<?php echo htmlspecialchars($datosCompletos['telefono'] ?? ''); ?>">
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para editar información de cuenta -->
  <div id="edit-account-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información de Cuenta</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form">
                  <div class="form-group">
                      <label for="username">Nombre de usuario</label>
                      <input type="text" id="username" name="nombre_usuario" value="<?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?>">
                  </div>
                  <div class="form-group">
                      <label for="email">Correo electrónico</label>
                      <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($usuarioActual['email']); ?>">
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para cambiar contraseña -->
  <div id="change-password-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Cambiar Contraseña</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form">
                  <div class="form-group">
                      <label for="current-password">Contraseña actual</label>
                      <input type="password" id="current-password">
                  </div>
                  <div class="form-group">
                      <label for="new-password">Nueva contraseña</label>
                      <input type="password" id="new-password">
                  </div>
                  <div class="form-group">
                      <label for="confirm-password">Confirmar nueva contraseña</label>
                      <input type="password" id="confirm-password">
                  </div>
                  
                  <div class="password-requirements">
                      <p>La contraseña debe cumplir con los siguientes requisitos:</p>
                      <ul>
                          <li>Mínimo 8 caracteres</li>
                          <li>Al menos una letra mayúscula</li>
                          <li>Al menos un número</li>
                          <li>Al menos un carácter especial</li>
                      </ul>
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Cambiar contraseña</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para editar información profesional -->
  <div id="edit-professional-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información Profesional</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form">
                  <div class="form-group">
                      <label for="position">Cargo</label>
                      <input type="text" id="position" name="cargo" value="<?php echo htmlspecialchars($datosCompletos['cargo'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="department">Departamento</label>
                      <input type="text" id="department" name="departamento" value="<?php echo htmlspecialchars($datosCompletos['departamento'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="hire-date">Fecha de contratación</label>
                      <input type="date" id="hire-date" name="fecha_contratacion" value="<?php echo htmlspecialchars($datosCompletos['fecha_contratacion'] ?? ''); ?>">
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>

  
  <!-- Modal para editar contacto de emergencia -->
  <div id="edit-emergency-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Contacto de Emergencia</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form">
                  <div class="form-group">
                      <label for="emergency-name">Nombre del contacto</label>
                      <input type="text" id="emergency-name" name="nombre_contacto" value="<?php echo htmlspecialchars($contactoEmergencia['nombre_contacto'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="emergency-phone">Teléfono principal</label>
                      <input type="tel" id="emergency-phone" name="telefono_principal" value="<?php echo htmlspecialchars($contactoEmergencia['telefono_principal'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="emergency-alt-phone">Teléfono alternativo</label>
                      <input type="tel" id="emergency-alt-phone" name="telefono_alternativo" value="<?php echo htmlspecialchars($contactoEmergencia['telefono_alternativo'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="emergency-email">Correo electrónico</label>
                      <input type="email" id="emergency-email" name="email" value="<?php echo htmlspecialchars($contactoEmergencia['email'] ?? ''); ?>">
                  </div>
                  
                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/perfil_a.js"></script>
</body>
</html>
