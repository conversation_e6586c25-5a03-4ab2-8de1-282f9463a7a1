document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn")
    const changePasswordBtn = document.querySelector(".change-password-btn")
    const changeAvatarBtn = document.querySelector(".change-avatar-btn")
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn")
    const modals = document.querySelectorAll(".modal-overlay")
  
    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const section = button.getAttribute("data-section")
        const modal = document.getElementById(`edit-${section}-modal`)
        if (modal) {
          modal.classList.add("active")
        }
      })
    })
  
    // Mostrar modal de cambio de contraseña
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener("click", () => {
        const modal = document.getElementById("change-password-modal")
        if (modal) {
          modal.classList.add("active")
        }
      })
    }
  
    // Simular cambio de avatar
    if (changeAvatarBtn) {
      changeAvatarBtn.addEventListener("click", () => {
        // Aquí se podría abrir un selector de archivos
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = "image/*"
        fileInput.style.display = "none"
  
        fileInput.addEventListener("change", (e) => {
          if (e.target.files && e.target.files[0]) {
            // Aquí se procesaría la imagen seleccionada
            // Por ahora solo mostramos un mensaje
            alert("Imagen seleccionada. En un entorno real, esta imagen se subiría al servidor.")
          }
        })
  
        document.body.appendChild(fileInput)
        fileInput.click()
        document.body.removeChild(fileInput)
      })
    }
  
    // Cerrar modales con botones de cierre
    modalCloseButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
  
    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })
  
    // Manejar envío de formularios (simulado)
    const forms = document.querySelectorAll(".edit-form")
    forms.forEach((form) => {
      form.addEventListener("submit", (e) => {
        e.preventDefault()
  
        // Aquí se procesarían los datos del formulario
        // Por ahora solo mostramos un mensaje y cerramos el modal
        alert("Cambios guardados correctamente.")
  
        const modal = form.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
  })
    